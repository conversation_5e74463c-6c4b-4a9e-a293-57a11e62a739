# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.get_conv_ai_dashboard_settings_response_model import GetConvAiDashboardSettingsResponseModel
from .raw_client import AsyncRawSettingsClient, RawSettingsClient
from .types.patch_conv_ai_dashboard_settings_request_charts_item import PatchConvAiDashboardSettingsRequestChartsItem

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SettingsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSettingsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSettingsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSettingsClient
        """
        return self._raw_client

    def get(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetConvAiDashboardSettingsResponseModel:
        """
        Retrieve Convai dashboard settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiDashboardSettingsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.dashboard.settings.get()
        """
        _response = self._raw_client.get(request_options=request_options)
        return _response.data

    def update(
        self,
        *,
        charts: typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConvAiDashboardSettingsResponseModel:
        """
        Update Convai dashboard settings for the workspace

        Parameters
        ----------
        charts : typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiDashboardSettingsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.dashboard.settings.update()
        """
        _response = self._raw_client.update(charts=charts, request_options=request_options)
        return _response.data


class AsyncSettingsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSettingsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSettingsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSettingsClient
        """
        return self._raw_client

    async def get(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetConvAiDashboardSettingsResponseModel:
        """
        Retrieve Convai dashboard settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiDashboardSettingsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.dashboard.settings.get()


        asyncio.run(main())
        """
        _response = await self._raw_client.get(request_options=request_options)
        return _response.data

    async def update(
        self,
        *,
        charts: typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConvAiDashboardSettingsResponseModel:
        """
        Update Convai dashboard settings for the workspace

        Parameters
        ----------
        charts : typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConvAiDashboardSettingsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.dashboard.settings.update()


        asyncio.run(main())
        """
        _response = await self._raw_client.update(charts=charts, request_options=request_options)
        return _response.data
