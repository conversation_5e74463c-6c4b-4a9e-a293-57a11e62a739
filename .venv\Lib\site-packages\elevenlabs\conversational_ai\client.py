# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

from .. import core
from ..core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.add_knowledge_base_response_model import AddKnowledgeBaseResponseModel
from ..types.post_workspace_secret_response_model import PostWorkspaceSecretResponseModel
from ..types.rag_document_index_response_model import RagDocumentIndexResponseModel
from ..types.rag_document_indexes_response_model import RagDocumentIndexesResponseModel
from ..types.rag_index_overview_response_model import RagIndexOverviewResponseModel
from .agents.client import AgentsClient, AsyncAgentsClient
from .batch_calls.client import AsyncBatchCallsClient, BatchCallsClient
from .conversations.client import AsyncConversationsClient, ConversationsClient
from .dashboard.client import AsyncDashboardClient, DashboardClient
from .knowledge_base.client import AsyncKnowledgeBase<PERSON>lient, KnowledgeBaseClient
from .llm_usage.client import Async<PERSON>lm<PERSON><PERSON><PERSON><PERSON><PERSON>, LlmUsageClient
from .mcp_servers.client import AsyncMcpServers<PERSON>lient, McpServersClient
from .phone_numbers.client import AsyncPhoneNumbersClient, PhoneNumbersClient
from .raw_client import AsyncRawConversationalAiClient, RawConversationalAiClient
from .secrets.client import AsyncSecretsClient, SecretsClient
from .settings.client import AsyncSettingsClient, SettingsClient
from .sip_trunk.client import AsyncSipTrunkClient, SipTrunkClient
from .twilio.client import AsyncTwilioClient, TwilioClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ConversationalAiClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawConversationalAiClient(client_wrapper=client_wrapper)
        self.conversations = ConversationsClient(client_wrapper=client_wrapper)

        self.twilio = TwilioClient(client_wrapper=client_wrapper)

        self.agents = AgentsClient(client_wrapper=client_wrapper)

        self.phone_numbers = PhoneNumbersClient(client_wrapper=client_wrapper)

        self.llm_usage = LlmUsageClient(client_wrapper=client_wrapper)

        self.knowledge_base = KnowledgeBaseClient(client_wrapper=client_wrapper)

        self.settings = SettingsClient(client_wrapper=client_wrapper)

        self.secrets = SecretsClient(client_wrapper=client_wrapper)

        self.batch_calls = BatchCallsClient(client_wrapper=client_wrapper)

        self.sip_trunk = SipTrunkClient(client_wrapper=client_wrapper)

        self.mcp_servers = McpServersClient(client_wrapper=client_wrapper)

        self.dashboard = DashboardClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawConversationalAiClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawConversationalAiClient
        """
        return self._raw_client

    def add_to_knowledge_base(
        self,
        *,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Upload a file or webpage URL to create a knowledge base document. <br> <Note> After creating the document, update the agent's knowledge base by calling [Update agent](/docs/conversational-ai/api-reference/agents/update-agent). </Note>

        Parameters
        ----------
        name : typing.Optional[str]
            A custom, human-readable name for the document.

        url : typing.Optional[str]
            URL to a page of documentation that the agent will have access to in order to interact with users.

        file : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.add_to_knowledge_base()
        """
        _response = self._raw_client.add_to_knowledge_base(
            name=name, url=url, file=file, request_options=request_options
        )
        return _response.data

    def get_document_rag_indexes(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> RagDocumentIndexesResponseModel:
        """
        Provides information about all RAG indexes of the specified knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagDocumentIndexesResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.get_document_rag_indexes(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get_document_rag_indexes(documentation_id, request_options=request_options)
        return _response.data

    def delete_document_rag_index(
        self, documentation_id: str, rag_index_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> RagDocumentIndexResponseModel:
        """
        Delete RAG index for the knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        rag_index_id : str
            The id of RAG index of document from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagDocumentIndexResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.delete_document_rag_index(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
            rag_index_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.delete_document_rag_index(
            documentation_id, rag_index_id, request_options=request_options
        )
        return _response.data

    def rag_index_overview(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> RagIndexOverviewResponseModel:
        """
        Provides total size and other information of RAG indexes used by knowledgebase documents

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagIndexOverviewResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.rag_index_overview()
        """
        _response = self._raw_client.rag_index_overview(request_options=request_options)
        return _response.data

    def update_secret(
        self, secret_id: str, *, name: str, value: str, request_options: typing.Optional[RequestOptions] = None
    ) -> PostWorkspaceSecretResponseModel:
        """
        Update an existing secret for the workspace

        Parameters
        ----------
        secret_id : str

        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostWorkspaceSecretResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.update_secret(
            secret_id="secret_id",
            name="name",
            value="value",
        )
        """
        _response = self._raw_client.update_secret(secret_id, name=name, value=value, request_options=request_options)
        return _response.data


class AsyncConversationalAiClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawConversationalAiClient(client_wrapper=client_wrapper)
        self.conversations = AsyncConversationsClient(client_wrapper=client_wrapper)

        self.twilio = AsyncTwilioClient(client_wrapper=client_wrapper)

        self.agents = AsyncAgentsClient(client_wrapper=client_wrapper)

        self.phone_numbers = AsyncPhoneNumbersClient(client_wrapper=client_wrapper)

        self.llm_usage = AsyncLlmUsageClient(client_wrapper=client_wrapper)

        self.knowledge_base = AsyncKnowledgeBaseClient(client_wrapper=client_wrapper)

        self.settings = AsyncSettingsClient(client_wrapper=client_wrapper)

        self.secrets = AsyncSecretsClient(client_wrapper=client_wrapper)

        self.batch_calls = AsyncBatchCallsClient(client_wrapper=client_wrapper)

        self.sip_trunk = AsyncSipTrunkClient(client_wrapper=client_wrapper)

        self.mcp_servers = AsyncMcpServersClient(client_wrapper=client_wrapper)

        self.dashboard = AsyncDashboardClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawConversationalAiClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawConversationalAiClient
        """
        return self._raw_client

    async def add_to_knowledge_base(
        self,
        *,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddKnowledgeBaseResponseModel:
        """
        Upload a file or webpage URL to create a knowledge base document. <br> <Note> After creating the document, update the agent's knowledge base by calling [Update agent](/docs/conversational-ai/api-reference/agents/update-agent). </Note>

        Parameters
        ----------
        name : typing.Optional[str]
            A custom, human-readable name for the document.

        url : typing.Optional[str]
            URL to a page of documentation that the agent will have access to in order to interact with users.

        file : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddKnowledgeBaseResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.add_to_knowledge_base()


        asyncio.run(main())
        """
        _response = await self._raw_client.add_to_knowledge_base(
            name=name, url=url, file=file, request_options=request_options
        )
        return _response.data

    async def get_document_rag_indexes(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> RagDocumentIndexesResponseModel:
        """
        Provides information about all RAG indexes of the specified knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagDocumentIndexesResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.get_document_rag_indexes(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_document_rag_indexes(documentation_id, request_options=request_options)
        return _response.data

    async def delete_document_rag_index(
        self, documentation_id: str, rag_index_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> RagDocumentIndexResponseModel:
        """
        Delete RAG index for the knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        rag_index_id : str
            The id of RAG index of document from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagDocumentIndexResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.delete_document_rag_index(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
                rag_index_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete_document_rag_index(
            documentation_id, rag_index_id, request_options=request_options
        )
        return _response.data

    async def rag_index_overview(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> RagIndexOverviewResponseModel:
        """
        Provides total size and other information of RAG indexes used by knowledgebase documents

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RagIndexOverviewResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.rag_index_overview()


        asyncio.run(main())
        """
        _response = await self._raw_client.rag_index_overview(request_options=request_options)
        return _response.data

    async def update_secret(
        self, secret_id: str, *, name: str, value: str, request_options: typing.Optional[RequestOptions] = None
    ) -> PostWorkspaceSecretResponseModel:
        """
        Update an existing secret for the workspace

        Parameters
        ----------
        secret_id : str

        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostWorkspaceSecretResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.update_secret(
                secret_id="secret_id",
                name="name",
                value="value",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update_secret(
            secret_id, name=name, value=value, request_options=request_options
        )
        return _response.data
