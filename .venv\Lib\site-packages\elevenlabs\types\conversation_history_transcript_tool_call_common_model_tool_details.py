# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class ConversationHistoryTranscriptToolCallCommonModelToolDetails_Client(UncheckedBaseModel):
    type: typing.Literal["client"] = "client"
    parameters: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ConversationHistoryTranscriptToolCallCommonModelToolDetails_Webhook(UncheckedBaseModel):
    type: typing.Literal["webhook"] = "webhook"
    method: str
    url: str
    headers: typing.Optional[typing.Dict[str, str]] = None
    path_params: typing.Optional[typing.Dict[str, str]] = None
    query_params: typing.Optional[typing.Dict[str, str]] = None
    body: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


ConversationHistoryTranscriptToolCallCommonModelToolDetails = typing_extensions.Annotated[
    typing.Union[
        ConversationHistoryTranscriptToolCallCommonModelToolDetails_Client,
        ConversationHistoryTranscriptToolCallCommonModelToolDetails_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
