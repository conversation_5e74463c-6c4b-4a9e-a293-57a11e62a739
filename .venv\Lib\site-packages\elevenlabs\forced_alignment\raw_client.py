# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from .. import core
from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.http_response import As<PERSON><PERSON>tt<PERSON><PERSON><PERSON>ponse, HttpResponse
from ..core.request_options import RequestOptions
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.forced_alignment_response_model import ForcedAlignmentResponseModel
from ..types.http_validation_error import HttpValidationError

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawForcedAlignmentClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self,
        *,
        file: core.File,
        text: str,
        enabled_spooled_file: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[ForcedAlignmentResponseModel]:
        """
        Force align an audio file to text. Use this endpoint to get the timing information for each character and word in an audio file based on a provided text transcript.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        text : str
            The text to align with the audio. The input text can be in any format, however diarization is not supported at this time.

        enabled_spooled_file : typing.Optional[bool]
            If true, the file will be streamed to the server and processed in chunks. This is useful for large files that cannot be loaded into memory. The default is false.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[ForcedAlignmentResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/forced-alignment",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "text": text,
                "enabled_spooled_file": enabled_spooled_file,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    ForcedAlignmentResponseModel,
                    construct_type(
                        type_=ForcedAlignmentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawForcedAlignmentClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self,
        *,
        file: core.File,
        text: str,
        enabled_spooled_file: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[ForcedAlignmentResponseModel]:
        """
        Force align an audio file to text. Use this endpoint to get the timing information for each character and word in an audio file based on a provided text transcript.

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        text : str
            The text to align with the audio. The input text can be in any format, however diarization is not supported at this time.

        enabled_spooled_file : typing.Optional[bool]
            If true, the file will be streamed to the server and processed in chunks. This is useful for large files that cannot be loaded into memory. The default is false.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[ForcedAlignmentResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/forced-alignment",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "text": text,
                "enabled_spooled_file": enabled_spooled_file,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    ForcedAlignmentResponseModel,
                    construct_type(
                        type_=ForcedAlignmentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
