# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations


class ElevenLabsEnvironment:
    PRODUCTION: ElevenLabsEnvironment
    PRODUCTION_US: ElevenLabsEnvironment
    PRODUCTION_EU: ElevenLabsEnvironment

    def __init__(self, *, base: str, wss: str):
        self.base = base
        self.wss = wss


ElevenLabsEnvironment.PRODUCTION = ElevenLabsEnvironment(
    base="https://api.elevenlabs.io", wss="wss://api.elevenlabs.io"
)
ElevenLabsEnvironment.PRODUCTION_US = ElevenLabsEnvironment(
    base="https://api.us.elevenlabs.io", wss="wss://api.elevenlabs.io"
)
ElevenLabsEnvironment.PRODUCTION_EU = ElevenLabsEnvironment(
    base="https://api.eu.residency.elevenlabs.io", wss="wss://api.elevenlabs.io"
)
