# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON>NDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClient<PERSON>rapper
from ...core.http_response import As<PERSON><PERSON>tt<PERSON><PERSON><PERSON>ponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.delete_workspace_member_response_model import DeleteWorkspaceMemberResponseModel
from ...types.http_validation_error import HttpValidationError
from ...types.update_workspace_member_response_model import UpdateWorkspaceMemberResponseModel
from .types.body_update_member_v_1_workspace_members_post_workspace_role import (
    BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole,
)

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawMembersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def update(
        self,
        *,
        email: str,
        is_locked: typing.Optional[bool] = OMIT,
        workspace_role: typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[UpdateWorkspaceMemberResponseModel]:
        """
        Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        is_locked : typing.Optional[bool]
            Whether to lock or unlock the user account.

        workspace_role : typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole]
            Role dictating permissions in the workspace.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[UpdateWorkspaceMemberResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
                "is_locked": is_locked,
                "workspace_role": workspace_role,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    UpdateWorkspaceMemberResponseModel,
                    construct_type(
                        type_=UpdateWorkspaceMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def delete(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[DeleteWorkspaceMemberResponseModel]:
        """
        Deletes a workspace member. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[DeleteWorkspaceMemberResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/members",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteWorkspaceMemberResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawMembersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def update(
        self,
        *,
        email: str,
        is_locked: typing.Optional[bool] = OMIT,
        workspace_role: typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[UpdateWorkspaceMemberResponseModel]:
        """
        Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        is_locked : typing.Optional[bool]
            Whether to lock or unlock the user account.

        workspace_role : typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole]
            Role dictating permissions in the workspace.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[UpdateWorkspaceMemberResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
                "is_locked": is_locked,
                "workspace_role": workspace_role,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    UpdateWorkspaceMemberResponseModel,
                    construct_type(
                        type_=UpdateWorkspaceMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def delete(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[DeleteWorkspaceMemberResponseModel]:
        """
        Deletes a workspace member. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[DeleteWorkspaceMemberResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/members",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteWorkspaceMemberResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
