# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ....core.api_error import ApiError
from ....core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ....core.http_response import AsyncHttpR<PERSON>ponse, HttpResponse
from ....core.request_options import RequestOptions
from ....core.serialization import convert_and_respect_annotation_metadata
from ....core.unchecked_base_model import construct_type
from ....errors.unprocessable_entity_error import UnprocessableEntityError
from ....types.get_conv_ai_dashboard_settings_response_model import GetConvAiDashboardSettingsResponseModel
from ....types.http_validation_error import HttpValidationError
from .types.patch_conv_ai_dashboard_settings_request_charts_item import PatchConvAiDashboardSettingsRequestChartsItem

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawSettingsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[GetConvAiDashboardSettingsResponseModel]:
        """
        Retrieve Convai dashboard settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetConvAiDashboardSettingsResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/settings/dashboard",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiDashboardSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiDashboardSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def update(
        self,
        *,
        charts: typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[GetConvAiDashboardSettingsResponseModel]:
        """
        Update Convai dashboard settings for the workspace

        Parameters
        ----------
        charts : typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetConvAiDashboardSettingsResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/settings/dashboard",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "charts": convert_and_respect_annotation_metadata(
                    object_=charts,
                    annotation=typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem],
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiDashboardSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiDashboardSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawSettingsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[GetConvAiDashboardSettingsResponseModel]:
        """
        Retrieve Convai dashboard settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetConvAiDashboardSettingsResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/settings/dashboard",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiDashboardSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiDashboardSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def update(
        self,
        *,
        charts: typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[GetConvAiDashboardSettingsResponseModel]:
        """
        Update Convai dashboard settings for the workspace

        Parameters
        ----------
        charts : typing.Optional[typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem]]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetConvAiDashboardSettingsResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/settings/dashboard",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "charts": convert_and_respect_annotation_metadata(
                    object_=charts,
                    annotation=typing.Sequence[PatchConvAiDashboardSettingsRequestChartsItem],
                    direction="write",
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiDashboardSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiDashboardSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
