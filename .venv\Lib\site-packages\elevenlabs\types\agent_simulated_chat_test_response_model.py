# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_history_analysis_common_model import ConversationHistoryAnalysisCommonModel
from .conversation_history_transcript_common_model_output import ConversationHistoryTranscriptCommonModelOutput


class AgentSimulatedChatTestResponseModel(UncheckedBaseModel):
    simulated_conversation: typing.List[ConversationHistoryTranscriptCommonModelOutput]
    analysis: ConversationHistoryAnalysisCommonModel

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
