# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .client_event import ClientEvent


class ConversationConfig(UncheckedBaseModel):
    text_only: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If enabled audio will not be processed and only text will be used, use to avoid audio pricing.
    """

    max_duration_seconds: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum duration of a conversation in seconds
    """

    client_events: typing.Optional[typing.List[ClientEvent]] = pydantic.Field(default=None)
    """
    The events that will be sent to the client
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
