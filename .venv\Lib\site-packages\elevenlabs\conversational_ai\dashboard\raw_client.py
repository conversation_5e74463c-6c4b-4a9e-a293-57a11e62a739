# This file was auto-generated by Fern from our API Definition.

from ...core.client_wrapper import AsyncC<PERSON>Wrapper, SyncClientWrapper


class RawDashboardClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper


class AsyncRawDashboardClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper
