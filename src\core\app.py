"""
Main application class for Whisp2 voice transcription application.
Coordinates all components and manages application lifecycle.
"""

import os
import threading
import tkinter as tk
import customtkinter as ctk
from pynput import keyboard
import traceback
import sys
import json
from typing import Optional, Dict, Any

# Import core utilities
from .logging_utils import debug_log, set_app_instance
from .settings_manager import SettingsManager
from .startup_manager import StartupManager

# Import managers
from src.ui.settings_dialog import SettingsDialog
from src.managers.audio_manager import AudioManager
from src.managers.transcription_manager import TranscriptionManager
from src.ui.ui_manager import UIManager
from src.managers.system_tray_manager import SystemTrayManager
from src.managers.audio_storage_manager import AudioStorageManager
from src.managers.transcription_history_manager import TranscriptionHistoryManager


# Set CustomTkinter appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")


class WhispApp:
  """Main application class that coordinates all components."""
  
  def __init__(self):
    """Initialize the Whisp application."""
    # Initialize root window first
    self.root = None
    self.keyboard_listener = None
    self.pressed_keys = set()
    self.ui_initialized = False
    
    # Recording state
    self.is_recording = False
    self.stop_recording = False
    self.recording_animation_active = False
    
    # Enhanced window dimensions tracking
    self.dropdown_visible = False
    self.saved_width = None  # Track saved width separately
    self.saved_height = None  # Track saved height separately
    self._resize_timer = None  # Debounce timer for resize events
    
    # Application state
    self.latest_amplitude_for_viz = 0.05  # For sharing amplitude between audio and UI threads
    self.is_minimized = False  # Track window minimization state
    self.waveform_points = []  # UIBarManager will use this for visualization data
    self.PAUSE_KEY = keyboard.Key.pause
    
    # Initialize settings manager first
    self.settings_manager = SettingsManager()
    
    # Initialize startup manager
    self.startup_manager = StartupManager()
    
    # Set up global exception handler
    sys.excepthook = self.handle_exception
    
    # Set up logging integration
    set_app_instance(self)
    debug_log("WhispApp initializing...", "AUDIT")
    
    # Create window BEFORE initializing managers that need it
    self._setup_window()
    self._initialize_managers()
    self._setup_keyboard_listener()
    self._post_initialization_checks()
    
    debug_log("WhispApp initialization complete.", "AUDIT")
  
  def _get_app_directory(self) -> str:
    """Get the application directory (where the exe or script is located)."""
    if getattr(sys, 'frozen', False):
      # Running as compiled executable
      return os.path.dirname(sys.executable)
    else:
      # Running as Python script - get the root project directory
      return os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
  
  def _initialize_managers(self) -> None:
    """Initialize all manager components."""
    try:
      # Initialize core components
      self.audio_manager = AudioManager(self)
      self.audio_storage_manager = AudioStorageManager(self)
      self.transcription_manager = TranscriptionManager(self)
      # Initialize SQLite transcription database (replaces JSON history)
      from src.managers.transcription_database import TranscriptionDatabase
      self.transcription_db = TranscriptionDatabase(self)
      
      # Migrate from old JSON history if it exists
      old_history_path = os.path.join(self._get_app_directory(), 'transcription_history.json')
      if os.path.exists(old_history_path):
        migrated_count = self.transcription_db.migrate_from_json(old_history_path)
        if migrated_count > 0:
          debug_log(f"Migrated {migrated_count} entries from JSON to SQLite", "INFO")
          # Backup the old file
          import shutil
          backup_path = old_history_path + '.backup'
          shutil.move(old_history_path, backup_path)
          debug_log(f"Backed up old history to {backup_path}", "INFO")
      self.ui_manager = UIManager(self)
      self.system_tray_manager = SystemTrayManager(self)
      
      debug_log("All managers initialized successfully", "INFO")
    except Exception as e:
      debug_log(f"Error initializing managers: {e}", "ERROR")
      raise
  
  def _setup_window(self) -> None:
    """Setup the main application window."""
    self.root = ctk.CTk()
    
    # Bind window events first
    self.root.protocol("WM_DELETE_WINDOW", self.on_close_window)
    self.root.bind("<Configure>", self._on_window_resize)
    
    # Apply window dimensions with smart restoration
    self._apply_window_dimensions(first_time=True)
    
    debug_log("Window setup complete", "INFO")
  
  def _setup_keyboard_listener(self) -> None:
    """Setup global keyboard listener for hotkeys."""
    try:
      self.keyboard_listener = keyboard.Listener(
        on_press=self.on_key_press,
        on_release=self.on_key_release
      )
      self.keyboard_listener.start()
      debug_log("Keyboard listener started", "INFO")
    except Exception as e:
      debug_log(f"Failed to start keyboard listener: {e}", "ERROR")
  
  def _post_initialization_checks(self) -> None:    """Perform post-initialization validation and setup."""    # Check if UI was initialized properly    if self.ui_initialized:      debug_log("UIManager has confirmed UI initialization.", "INFO")    else:      debug_log("CRITICAL: UIManager did not set ui_initialized to True post setup!", "ERROR")        # Check API client availability    if self.transcription_manager and not self.transcription_manager.api_client:      active_api = self.settings_manager.get('active_api', 'N/A')      debug_log(f"Warning: Transcription manager initialized, but API client for '{active_api}' is not available. Check API key & settings.", "WARNING")      if self.ui_manager:        self.ui_manager.update_main_status_message(f"API client for {active_api} failed. Check settings.", "ERROR")        # Sync startup setting with actual Windows startup state    self._sync_startup_setting()
  
  def _apply_first_time_dimensions(self) -> None:
    """Apply dimensions for first startup - always collapsed with saved width."""
    initial_height = 100  # Collapsed height (fixed)
    initial_width = self.saved_width or 630  # Use saved width or default

    # Get saved position
    x, y = self._get_saved_position()
    if x is None or y is None:
      x = (self.root.winfo_screenwidth() - initial_width) // 2
      y = (self.root.winfo_screenheight() - initial_height) // 2

    geometry_string = f"{initial_width}x{initial_height}+{x}+{y}"
    debug_log(f"[WINDOW] Setting first-time geometry: {geometry_string}")
    self.root.geometry(geometry_string)
    debug_log(f"[WINDOW] First startup: {initial_width}x{initial_height}+{x}+{y} (width restored, height fixed)")

  def _apply_dropdown_dimensions(self) -> None:
    """Apply dimensions when dropdown is visible - restore both width and height."""
    width = self.saved_width or 630
    height = self.saved_height or 715  # Default expanded height
    x, y = self._get_saved_position()
    geometry_string = f"{width}x{height}"
    if x is not None and y is not None:
      geometry_string += f"+{x}+{y}"

    self.root.geometry(geometry_string)
    debug_log(f"[WINDOW] Dropdown open - restored both: {geometry_string}")

  def _apply_collapsed_dimensions(self) -> None:
    """Apply dimensions when dropdown is hidden - only restore width."""
    width = self.saved_width or 630
    height = 100  # Fixed collapsed height
    x, y = self._get_saved_position()
    geometry_string = f"{width}x{height}"
    if x is not None and y is not None:
      geometry_string += f"+{x}+{y}"

    self.root.geometry(geometry_string)
    debug_log(f"[WINDOW] Dropdown closed - width restored, height fixed: {geometry_string}")

  def _apply_window_dimensions(self, first_time: bool = False) -> None:
    """Apply window dimensions with smart restoration logic."""
    try:
      # Load saved dimensions
      self._load_saved_dimensions()

      debug_log(f"[WINDOW] Applying dimensions - first_time={first_time}, dropdown_visible={self.dropdown_visible}")
      debug_log(f"[WINDOW] Saved dimensions: width={self.saved_width}, height={self.saved_height}")

      if first_time:
        self._apply_first_time_dimensions()
      elif self.dropdown_visible:
        self._apply_dropdown_dimensions()
      else:
        self._apply_collapsed_dimensions()

    except Exception as e:
      debug_log(f"[WINDOW] Error applying dimensions: {e}")
      # Fallback to hardcoded defaults
      self.root.geometry("630x100+100+100")
  
  def _load_saved_dimensions(self) -> None:
    """Load saved dimensions from file."""
    try:
      dimensions_file = os.path.join(self._get_app_directory(), 'window_dimensions.json')
      if os.path.exists(dimensions_file):
        with open(dimensions_file, 'r') as f:
          data = json.load(f)
          
          # Support both old and new format
          if 'geometry' in data:
            # Old format - parse geometry string
            geometry = data['geometry']
            if 'x' in geometry and '+' in geometry:
              size_part = geometry.split('+')[0]
              if 'x' in size_part:
                width, height = size_part.split('x')
                self.saved_width = int(width)
                self.saved_height = int(height)
          
          # New format - direct width/height values (preferred)
          if 'width' in data:
            self.saved_width = data['width']
          if 'height' in data:
            self.saved_height = data['height']
          
          debug_log(f"[WINDOW] Loaded dimensions: width={self.saved_width}, height={self.saved_height}")
    except Exception as e:
      debug_log(f"[WINDOW] Error loading saved dimensions: {e}")
      self.saved_width = None
      self.saved_height = None
  
  def _get_saved_position(self) -> tuple:
    """Get saved window position."""
    try:
      dimensions_file = os.path.join(self._get_app_directory(), 'window_dimensions.json')
      if os.path.exists(dimensions_file):
        with open(dimensions_file, 'r') as f:
          data = json.load(f)
          
          # Try new format first
          if 'x' in data and 'y' in data:
            return data['x'], data['y']
          
          # Fall back to parsing geometry string
          if 'geometry' in data:
            geometry = data['geometry']
            if '+' in geometry:
              pos_part = geometry.split('+', 1)[1]
              if '+' in pos_part:
                x_str, y_str = pos_part.split('+', 1)
                return int(x_str), int(y_str)
    except Exception as e:
      debug_log(f"[WINDOW] Error getting saved position: {e}")
    
    return None, None
  
  def handle_exception(self, exc_type, exc_value, exc_traceback) -> bool:
    """Global exception handler to prevent application crashes."""
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    debug_log(f"EXCEPTION: {error_msg}")
    
    # Try to show an error message in the UI if possible
    try:
      if hasattr(self, 'ui_manager') and self.ui_manager:
        self.ui_manager.show_error(f"An error occurred: {exc_value}")
    except:
      pass  # If UI fails, just log the error
    
    # Don't quit on exceptions, just log them
    return True
  
  def on_key_press(self, key) -> None:
    """Handle global key press events."""
    try:
      self.pressed_keys.add(key)
      if key == self.PAUSE_KEY:
        self.toggle_recording()
    except Exception as e:
      debug_log(f"Error in key press handler: {e}", "ERROR")
  
  def on_key_release(self, key) -> None:
    """Handle global key release events."""
    try:
      self.pressed_keys.discard(key)
    except Exception as e:
      debug_log(f"Error in key release handler: {e}", "ERROR")
  
  def toggle_recording(self) -> None:
    """Toggle recording state between start and stop."""
    try:
      if not self.is_recording:
        self.start_recording()
      else:
        self.stop_recording_process()
    except Exception as e:
      debug_log(f"Error toggling recording: {e}", "ERROR")
      if self.ui_manager:
        self.ui_manager.update_main_status_message(f"Recording error: {str(e)}", "ERROR")
  
  def start_recording(self) -> None:
    """Start the recording process."""
    if self.is_recording:
      debug_log("Recording already in progress", "WARNING")
      return
    
    debug_log("Starting recording...", "INFO")
    self.is_recording = True
    self.stop_recording = False
    
    # Update UI
    if self.ui_manager:
      self.ui_manager.update_ui_for_recording_start()
    
    # Update system tray
    if self.system_tray_manager:
      self.system_tray_manager.update_icon(is_recording=True)
    
    # Start audio recording
    if self.audio_manager:
      self.audio_manager.start_recording()
  
  def stop_recording_process(self) -> None:
    """Stop the recording process."""
    if not self.is_recording:
      debug_log("No recording in progress", "WARNING")
      return
    
    debug_log("Stopping recording...", "INFO")
    self.stop_recording = True
    
    # Stop audio recording
    if self.audio_manager:
      self.audio_manager.stop_recording()
  
  def reset_recording_ui_and_state(self) -> None:
    """Reset recording state and UI after recording completion."""
    debug_log("Resetting app recording state.", "INFO")
    
    if self.ui_manager:
      self.ui_manager.update_ui_for_recording_stop()
    
    self.is_recording = False
    self.stop_recording = False
    
    # Ensure the bar manager shows the default prompt
    if (self.ui_manager and hasattr(self.ui_manager, 'bar_manager') and 
        self.ui_manager.bar_manager):
      self.ui_manager.bar_manager._draw_status_text_on_canvas(text_key="initial_prompt")
    
    # Ensure tray icon is reset to normal
    if self.system_tray_manager:
      self.system_tray_manager.update_icon(is_recording=False)
  
  def handle_successful_transcription(self, transcript: str) -> None:
    """Handle successful transcription result."""
    debug_log(f"Transcription successful: {transcript[:50]}...", "INFO")
    
    # Paste the transcript to the focused window using clipboard method
    if self.transcription_manager:
      self.transcription_manager.paste_text(transcript)
    
    if self.ui_manager:
      self.ui_manager.display_transcription_result(transcript)
    
    self.reset_recording_ui_and_state()
  
  def handle_failed_transcription(self, error_message: str) -> None:
    """Handle failed transcription."""
    debug_log(f"Transcription failed: {error_message}", "ERROR")
    
    if self.ui_manager:
      self.ui_manager.display_transcription_error(error_message)
    
    self.reset_recording_ui_and_state()
  
  def on_close_window(self) -> None:
    """Handle window close event."""
    close_behavior = self.settings_manager.get('close_behavior', 'minimize_to_tray')
    
    if close_behavior == 'minimize_to_tray':
      if self.system_tray_manager:
        self.system_tray_manager.minimize_to_tray()
      else:
        debug_log("System tray manager not available, exiting instead", "WARNING")
        self.quit_app()
    else:
      self.quit_app()
  
  def on_minimize(self, event=None) -> None:
    """Minimize application to system tray."""
    try:
      if self.system_tray_manager:
        self.system_tray_manager.minimize_to_tray()
      else:
        # Fallback: just hide the window if no tray manager
        self.root.withdraw()
        debug_log("No system tray manager available, window hidden", "WARNING")
      debug_log("Application minimized to tray", "INFO")
    except Exception as e:
      debug_log(f"Error minimizing to tray: {e}", "ERROR")
  
  def on_restore(self, event=None) -> None:
    """Restore application from system tray."""
    try:
      if self.system_tray_manager:
        self.system_tray_manager.show_window()
      else:
        # Fallback if no tray manager
        self.root.deiconify()  # Show the window
        self.root.lift()  # Bring to front
        self.root.focus_force()  # Give focus
      debug_log("Application restored from tray", "INFO")
    except Exception as e:
      debug_log(f"Error restoring from tray: {e}", "ERROR")
  
  def quit_app(self) -> None:
    """Quit the application completely."""
    debug_log("Quitting application...", "AUDIT")
    
    try:
      # Stop recording if active
      if self.is_recording:
        self.stop_recording_process()
      
      # Stop keyboard listener
      if self.keyboard_listener:
        self.keyboard_listener.stop()
      
      # Save settings
      self.settings_manager.save_settings()
      
      # Save window dimensions
      self._save_window_dimensions()
      
      # Clean up managers
      if hasattr(self, 'system_tray_manager') and self.system_tray_manager:
        self.system_tray_manager.cleanup()
      
      # Destroy UI
      if self.root:
        self.root.quit()
        self.root.destroy()
      
      debug_log("Application quit successfully", "AUDIT")
    except Exception as e:
      debug_log(f"Error during application quit: {e}", "ERROR")
  
  def _save_window_dimensions(self) -> None:
    """Save current window dimensions to file."""
    try:
      geometry = self.root.geometry()
      dimensions = {"geometry": geometry}
      
      dimensions_file = os.path.join(self._get_app_directory(), 'window_dimensions.json')
      with open(dimensions_file, 'w') as f:
        json.dump(dimensions, f)
      
      debug_log(f"Window dimensions saved: {geometry}", "INFO")
    except Exception as e:
      debug_log(f"Error saving window dimensions: {e}", "ERROR")
  
  def run(self) -> None:
    """Start the application main loop."""
    try:
      debug_log("Starting application main loop", "AUDIT")
      self.root.mainloop()
    except Exception as e:
      debug_log(f"Error in main loop: {e}", "ERROR")
      raise
    finally:
      debug_log("Application main loop ended", "AUDIT")
  
  # Legacy compatibility methods - these provide backward compatibility
  # with existing code that expects certain methods/properties
  
  @property
  def settings(self) -> Dict[str, Any]:
    """Legacy property for backward compatibility."""
    return self.settings_manager.settings
  
  def _load_settings(self) -> Dict[str, Any]:
    """Legacy method for backward compatibility."""
    return self.settings_manager.settings
  
  def _save_settings(self) -> None:
    """Legacy method for backward compatibility."""
    self.settings_manager.save_settings()
  
  def reload_api_client(self) -> None:
    """Reload the API client - delegates to transcription manager."""
    if self.transcription_manager:
      self.transcription_manager.reload_api_client()

  def update_api_status(self, api_name: str, is_valid: bool) -> None:
    """Update API status in settings and transcription manager."""
    try:
      debug_log(f"Updating API status for {api_name}: {is_valid}", "INFO")

      # Update settings manager
      self.settings_manager.set_api_status(api_name, is_valid)

      # Update transcription manager status if it exists
      if hasattr(self, 'transcription_manager') and self.transcription_manager:
        if not hasattr(self.transcription_manager, 'api_statuses'):
          self.transcription_manager.api_statuses = {}
        self.transcription_manager.api_statuses[api_name] = {'valid': is_valid}

      # Save settings to file
      self.settings_manager.save_settings()
      debug_log(f"Saved API status for {api_name}: {is_valid}", "INFO")

    except Exception as e:
      debug_log(f"Error in update_api_status: {e}", "ERROR")

  def test_api_connection(self, api_name: str, api_client) -> bool:
    """Test API connection using the appropriate method for each API."""
    try:
      debug_log(f"Testing {api_name} connection", "INFO")

      if self.transcription_manager:
        return self.transcription_manager.validate_api_connection(api_name, api_client)
      else:
        debug_log("TranscriptionManager not available for API testing", "ERROR")
        return False

    except Exception as e:
      debug_log(f"Error testing {api_name} connection: {e}", "ERROR")
      return False
  
  def on_ui_initialized(self) -> None:
    """Called when UI initialization is complete."""
    self.ui_initialized = True
    debug_log("UI initialization confirmed by UIManager", "INFO")
  
  def _on_window_resize(self, event):
    """Handle window resize events with smart width/height tracking."""
    # Only handle resize events for the main window
    if event.widget != self.root:
      return
    
    # Cancel any existing timer
    if self._resize_timer:
      self._resize_timer.cancel()
    
    # Debounce the save operation (wait 500ms after last resize)
    self._resize_timer = threading.Timer(0.5, self._save_smart_window_dimensions)
    self._resize_timer.start()
  
  def _save_smart_window_dimensions(self) -> None:
    """Save window dimensions based on dropdown state."""
    try:
      geometry = self.root.geometry()
      # Parse geometry: "widthxheight+x+y"
      size_part, pos_part = geometry.split('+', 1)
      width, height = size_part.split('x')
      x_y_parts = pos_part.split('+')
      x, y = int(x_y_parts[0]), int(x_y_parts[1])
      
      width, height = int(width), int(height)
      
      # Load existing data
      dimensions_file = os.path.join(self._get_app_directory(), 'window_dimensions.json')
      data = {}
      if os.path.exists(dimensions_file):
        try:
          with open(dimensions_file, 'r') as f:
            data = json.load(f)
        except json.JSONDecodeError:
          data = {}
      
      # Always save position
      data['x'] = x
      data['y'] = y
      
      # Smart dimension saving based on dropdown state
      if self.dropdown_visible:
        # Dropdown is open - save both width and height
        data['width'] = width
        data['height'] = height
        self.saved_width = width
        self.saved_height = height
        debug_log(f"[WINDOW] Dropdown open - saved both: {width}x{height}+{x}+{y}")
      else:
        # Dropdown is closed - only save width, not height
        data['width'] = width
        self.saved_width = width
        # Don't update height when dropdown is closed
        if 'height' not in data and self.saved_height:
          data['height'] = self.saved_height  # Preserve existing height
        debug_log(f"[WINDOW] Dropdown closed - saved width only: {width} (height preserved)")
      
      # Also save legacy geometry for backward compatibility
      data['geometry'] = geometry
      
      # Write the file
      with open(dimensions_file, 'w') as f:
        json.dump(data, f, indent=2)
      
    except Exception as e:
      debug_log(f"[WINDOW] Error saving smart dimensions: {e}")
  
  def set_dropdown_visible(self, visible: bool) -> None:
    """Update dropdown visibility state for smart window handling."""
    old_state = self.dropdown_visible
    self.dropdown_visible = visible
    
    if old_state != visible:
      debug_log(f"[WINDOW] Dropdown state changed: {old_state} -> {visible}")
      
      # If dropdown just opened, restore height
      if visible and not old_state:
        if self.saved_height:
          current_geometry = self.root.geometry()
          width = current_geometry.split('x')[0]
          pos_part = '+'.join(current_geometry.split('+')[1:])
          new_geometry = f"{width}x{self.saved_height}+{pos_part}"
          self.root.geometry(new_geometry)
          debug_log(f"[WINDOW] Dropdown opened - restored height: {self.saved_height}")
  
  def _sync_startup_setting(self) -> None:
    """Sync the startup setting with actual Windows startup state."""
    try:
      setting_value = self.settings_manager.get('add_to_startup', False)
      success = self.startup_manager.sync_with_setting(setting_value)
      
      if success:
        debug_log(f"Startup setting synced successfully: {setting_value}", "INFO")
      else:
        debug_log(f"Failed to sync startup setting: {setting_value}", "ERROR")
        # Update UI to reflect actual state if possible
        if self.ui_manager and hasattr(self.ui_manager, 'settings_tab'):
          actual_state = self.startup_manager.is_startup_enabled()
          self.settings_manager.set('add_to_startup', actual_state)
          debug_log(f"Updated setting to match actual state: {actual_state}", "INFO")
    except Exception as e:
      debug_log(f"Error syncing startup setting: {e}", "ERROR")
 