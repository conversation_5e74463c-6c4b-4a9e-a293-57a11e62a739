# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .feature_status_common_model import FeatureStatusCommonModel


class FeaturesUsageCommonModel(UncheckedBaseModel):
    language_detection: typing.Optional[FeatureStatusCommonModel] = None
    transfer_to_agent: typing.Optional[FeatureStatusCommonModel] = None
    transfer_to_number: typing.Optional[FeatureStatusCommonModel] = None
    multivoice: typing.Optional[FeatureStatusCommonModel] = None
    pii_zrm_workspace: typing.Optional[bool] = None
    pii_zrm_agent: typing.Optional[bool] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
