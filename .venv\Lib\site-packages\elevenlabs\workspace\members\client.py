# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON><PERSON><PERSON>, Sync<PERSON><PERSON><PERSON>rapper
from ...core.request_options import RequestOptions
from ...types.delete_workspace_member_response_model import DeleteWorkspaceMemberResponseModel
from ...types.update_workspace_member_response_model import UpdateWorkspaceMemberResponseModel
from .raw_client import AsyncRawM<PERSON>bersClient, RawMembersClient
from .types.body_update_member_v_1_workspace_members_post_workspace_role import (
    BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole,
)

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class MembersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawMembersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawMembersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawMembersClient
        """
        return self._raw_client

    def update(
        self,
        *,
        email: str,
        is_locked: typing.Optional[bool] = OMIT,
        workspace_role: typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateWorkspaceMemberResponseModel:
        """
        Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        is_locked : typing.Optional[bool]
            Whether to lock or unlock the user account.

        workspace_role : typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole]
            Role dictating permissions in the workspace.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateWorkspaceMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.members.update(
            email="email",
        )
        """
        _response = self._raw_client.update(
            email=email, is_locked=is_locked, workspace_role=workspace_role, request_options=request_options
        )
        return _response.data

    def delete(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceMemberResponseModel:
        """
        Deletes a workspace member. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.members.delete(
            email="email",
        )
        """
        _response = self._raw_client.delete(email=email, request_options=request_options)
        return _response.data


class AsyncMembersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawMembersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawMembersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawMembersClient
        """
        return self._raw_client

    async def update(
        self,
        *,
        email: str,
        is_locked: typing.Optional[bool] = OMIT,
        workspace_role: typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateWorkspaceMemberResponseModel:
        """
        Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        is_locked : typing.Optional[bool]
            Whether to lock or unlock the user account.

        workspace_role : typing.Optional[BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole]
            Role dictating permissions in the workspace.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateWorkspaceMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.members.update(
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            email=email, is_locked=is_locked, workspace_role=workspace_role, request_options=request_options
        )
        return _response.data

    async def delete(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceMemberResponseModel:
        """
        Deletes a workspace member. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            Email of the target user.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.members.delete(
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(email=email, request_options=request_options)
        return _response.data
