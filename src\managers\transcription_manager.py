import asyncio
import codecs
import json
import os
import threading
import time
import traceback
import sys
from datetime import datetime
from deepgram import DeepgramClient, PrerecordedOptions
from deepgram.errors import DeepgramApiKeyError
import requests
from openai import OpenAI
from elevenlabs.client import ElevenLabs
from groq import Groq
import tkinter as tk
from pynput.keyboard import Controller, Key
from pathlib import Path
import win32gui
import win32clipboard

# Import debug_log from new location
from src.core.logging_utils import debug_log, essential_log

# Import the new API client manager
from src.managers.api_client_manager import get_api_client_manager

# HARDCORE DEBUG - Write to a separate audit log for aggressive debugging
def audit_log(message, level="INFO"):
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        with open("whisp_audit.log", "a", encoding='utf-8') as f:
            f.write(f"{timestamp} - [{level}] - {message}\n")
        # Debug logging to audit log
    except Exception as e:
        print(f"Error writing to audit log: {e}")

# HARDCORE DEBUG - Log memory address of objects
def log_obj_info(obj, name):
    try:
        obj_id = id(obj)
        obj_type = type(obj).__name__
        obj_addr = hex(obj_id)
        obj_hash = hash(obj) if obj is not None else "N/A"
        audit_log(f"OBJECT: {name} = {obj_type}@{obj_addr} (hash={obj_hash})")
        return f"{obj_type}@{obj_addr}"
    except Exception as e:
        audit_log(f"Error in log_obj_info for {name}: {e}", "ERROR")
        return "Error logging object"

# HARDCORE DEBUG - Dump contents of a dictionary
def dump_dict(d, name):
    try:
        if d is None:
            audit_log(f"DICT: {name} = None")
            return
        audit_log(f"DICT: {name} contains {len(d)} items:")
        for k, v in d.items():
            # Limit length to avoid massive output
            v_str = str(v)
            if len(v_str) > 100:
                v_str = v_str[:100] + "..."
            audit_log(f"  {name}[{k}] = {v_str}")
    except Exception as e:
        audit_log(f"Error in dump_dict for {name}: {e}", "ERROR")

class TranscriptionManager:
    def __init__(self, app):
        audit_log("=====================================================", "CRITICAL")
        audit_log("TRANSCRIPTION MANAGER INITIALIZATION STARTING", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
        
        # Log callers
        current_frame = sys._getframe(1)
        caller_info = f"{current_frame.f_code.co_filename}:{current_frame.f_lineno} - {current_frame.f_code.co_name}"
        audit_log(f"Called from: {caller_info}", "CRITICAL")
        
        self.app = app
        log_obj_info(app, "self.app")

        self.api_client = None
        self.active_api = None
        self.is_processing = False
        self.last_transcript = ""

        # Initialize API client manager for resource pooling
        self.api_client_manager = get_api_client_manager()
        
        audit_log("TranscriptionManager.__init__: Initial state - api_client=None, active_api=None", "CRITICAL")
        
        try:
            # Log important dirs and files
            cwd = os.getcwd()
            audit_log(f"Current working directory: {cwd}", "CRITICAL")
            settings_path = os.path.join(cwd, 'settings.json')
            audit_log(f"Checking if settings.json exists at: {settings_path}", "CRITICAL")
            audit_log(f"settings.json exists: {os.path.exists(settings_path)}", "CRITICAL")
            
            if os.path.exists(settings_path):
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings_content = f.read()
                    audit_log(f"settings.json RAW CONTENT: {settings_content}", "CRITICAL")
                    # Try to parse it
                    try:
                        parsed = json.loads(settings_content)
                        audit_log(f"settings.json parsed successfully. active_api = {parsed.get('active_api', 'NOT FOUND')}", "CRITICAL")
                        dump_dict(parsed, "settings.json")
                    except json.JSONDecodeError as je:
                        audit_log(f"settings.json FAILED TO PARSE: {je}", "CRITICAL")
                except Exception as fe:
                    audit_log(f"Failed to read settings.json: {fe}", "CRITICAL")
            
            # Read settings file
            audit_log("TranscriptionManager.__init__: Calling read_settings_file()...", "CRITICAL")
            self.read_settings_file()
            
            init_active_api = self.active_api
            audit_log(f"TranscriptionManager.__init__: Active API after read_settings_file: {init_active_api}", "CRITICAL")
            
            # Load settings
            audit_log("TranscriptionManager.__init__: Calling load_settings()...", "CRITICAL")
            self.load_settings()
            
            audit_log(f"TranscriptionManager.__init__: Active API after load_settings: {self.active_api}", "CRITICAL")
            if self.api_client:
                client_info = log_obj_info(self.api_client, "self.api_client after load_settings")
                audit_log(f"API Client type: {type(self.api_client).__name__}", "CRITICAL")
            else:
                audit_log("API Client is None after load_settings", "CRITICAL")
            
            # Check if API changed
            if self.active_api != init_active_api:
                audit_log(f"TranscriptionManager.__init__: CRITICAL API MISMATCH - Started with '{init_active_api}', now '{self.active_api}'", "CRITICAL")
                self.active_api = init_active_api
                audit_log(f"TranscriptionManager.__init__: Forcing API back to '{init_active_api}' and reloading...", "CRITICAL")
                self.load_settings()
                audit_log(f"TranscriptionManager.__init__: After forced reload, active_api={self.active_api}", "CRITICAL")
                if self.api_client:
                    client_info = log_obj_info(self.api_client, "self.api_client after forced reload")
                    audit_log(f"API Client type after forced reload: {type(self.api_client).__name__}", "CRITICAL")
                else:
                    audit_log("API Client is STILL None after forced reload", "CRITICAL")
            
            # Verify client type
            expected_client_types = {
                'deepgram': DeepgramClient, 'openai': OpenAI, 'groq': Groq, 'elevenlabs': ElevenLabs
            }
            
            expected_type = expected_client_types.get(self.active_api)
            audit_log(f"TranscriptionManager.__init__: Expected client type for '{self.active_api}': {expected_type.__name__ if expected_type else 'None'}", "CRITICAL")
            
            if expected_type and self.api_client:
                is_correct_type = isinstance(self.api_client, expected_type)
                audit_log(f"TranscriptionManager.__init__: Client is correct type: {is_correct_type}", "CRITICAL")
                
                if not is_correct_type:
                    audit_log(f"TranscriptionManager.__init__: WRONG CLIENT TYPE! Expected {expected_type.__name__}, got {type(self.api_client).__name__}", "CRITICAL")
                    audit_log("TranscriptionManager.__init__: Forcing client to None and reloading...", "CRITICAL")
                    self.api_client = None
                    self.load_settings()
                    
                    if self.api_client:
                        new_type = type(self.api_client).__name__
                        audit_log(f"TranscriptionManager.__init__: After type correction, client is now: {new_type}", "CRITICAL")
                        is_correct_type_now = isinstance(self.api_client, expected_type)
                        audit_log(f"TranscriptionManager.__init__: Client is now correct type: {is_correct_type_now}", "CRITICAL")
                    else:
                        audit_log("TranscriptionManager.__init__: Client is STILL None after type correction attempt", "CRITICAL")
            
        except Exception as e:
            audit_log(f"TranscriptionManager.__init__: CRITICAL ERROR during initialization: {e}", "ERROR")
            audit_log(f"Traceback: {traceback.format_exc()}", "ERROR")
            if not self.app.ui_initialized:
                audit_log("UI not initialized, setting up UI...", "WARN")
                self.app.ui_manager.setup_ui()
            self.app.ui_manager.show_message(f"Error: {str(e)}", "error")
        
        if self.api_client is None:
            audit_log("TranscriptionManager.__init__: API client is None after initialization!", "CRITICAL")
            if not self.app.ui_initialized:
                self.app.ui_manager.setup_ui()
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text="Transcription API not configured. Please check settings.", text_color="orange"
                ))
        
        # Start transcription thread
        audit_log("TranscriptionManager.__init__: Starting transcription thread...", "INFO")
        # self.start_transcription_thread() # DISABLED
        
        # Final state logging
        audit_log(f"TranscriptionManager.__init__: Initialization complete with active_api={self.active_api}", "CRITICAL")
        if self.api_client:
            final_client_info = log_obj_info(self.api_client, "Final API client")
            audit_log(f"TranscriptionManager.__init__: Final client type: {type(self.api_client).__name__}", "CRITICAL")
        else:
            audit_log("TranscriptionManager.__init__: Final API client is None!", "CRITICAL")
        
        audit_log("=====================================================", "CRITICAL")
        audit_log("TRANSCRIPTION MANAGER INITIALIZATION COMPLETE", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
    
    def get_resource_path(self, relative_path):
        """Get the correct path for resources in both development and PyInstaller environments"""
        try:
            audit_log(f"get_resource_path: Looking for: {relative_path}", "DEBUG")
            base_path = getattr(sys, '_MEIPASS', os.path.abspath(os.path.dirname(__file__)))
            full_path = os.path.join(base_path, relative_path)
            audit_log(f"get_resource_path: Resolved to: {full_path} (exists: {os.path.exists(full_path)})", "DEBUG")
            return full_path
        except Exception as e:
            audit_log(f"get_resource_path: Error: {e}", "ERROR")
            return os.path.join(os.path.abspath(os.path.dirname(__file__)), relative_path)
    
    def read_settings_file(self):
        """Read settings file to get active_api"""
        audit_log("=====================================================", "CRITICAL") 
        audit_log("READ_SETTINGS_FILE STARTED", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
        
        try:
            possible_paths = [
                self.get_resource_path('settings.json'),
                'settings.json',
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'settings.json')
            ]
            
            audit_log(f"read_settings_file: Possible paths to check: {possible_paths}", "CRITICAL")
            
            settings_read = False
            settings_content = None
            
            # Check all possible paths
            for path in possible_paths:
                audit_log(f"read_settings_file: Checking path: {path} (exists: {os.path.exists(path)})", "CRITICAL")
                
                if os.path.exists(path):
                    audit_log(f"read_settings_file: Found settings file at: {path}", "CRITICAL")
                    
                    try:
                        # First read raw content
                        with open(path, 'r', encoding='utf-8') as f:
                            settings_content = f.read()
                        
                        audit_log(f"read_settings_file: Raw content from {path}: {settings_content}", "CRITICAL")
                        
                        # Now try to parse it
                        settings = json.loads(settings_content)
                        dump_dict(settings, f"settings from {path}")
                        
                        # Extract active_api
                        self.active_api = settings.get('active_api', 'deepgram')
                        audit_log(f"read_settings_file: Successfully extracted active_api = '{self.active_api}' from {path}", "CRITICAL")
                        settings_read = True
                        break
                    except json.JSONDecodeError as je:
                        audit_log(f"read_settings_file: Failed to parse settings from {path}: {je}", "ERROR")
                        audit_log(f"read_settings_file: Invalid JSON content: {settings_content}", "ERROR")
                        continue
                    except Exception as e:
                        audit_log(f"read_settings_file: Error reading from {path}: {e}", "ERROR")
                        continue
            
            # If we couldn't read from any path
            if not settings_read:
                audit_log("read_settings_file: Could not read settings from any path, defaulting to 'deepgram'", "CRITICAL")
                self.active_api = 'deepgram'
            
            audit_log(f"read_settings_file: Final active_api = '{self.active_api}'", "CRITICAL")
            
        except Exception as e:
            audit_log(f"read_settings_file: Unhandled error: {e}", "ERROR")
            audit_log(f"read_settings_file: Traceback: {traceback.format_exc()}", "ERROR")
            self.active_api = 'deepgram'
            audit_log("read_settings_file: Defaulted to 'deepgram' due to error", "CRITICAL")
        
        audit_log("=====================================================", "CRITICAL")
        audit_log("READ_SETTINGS_FILE COMPLETED", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
    
    def load_settings(self):
        """Load settings and initialize the appropriate API client"""
        audit_log("=====================================================", "CRITICAL")
        audit_log("LOAD_SETTINGS STARTED", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
        audit_log(f"load_settings: ENTERING with active_api = '{self.active_api}'", "CRITICAL")
        
        try:
            # Clear API client first
            old_client = "None" if self.api_client is None else type(self.api_client).__name__
            audit_log(f"load_settings: Clearing API client (was: {old_client})", "CRITICAL")
            self.api_client = None
            
            # Paths to check
            possible_paths = [
                self.get_resource_path('settings.json'),
                'settings.json',
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'settings.json')
            ]
            
            audit_log(f"load_settings: Possible settings paths: {possible_paths}", "CRITICAL")
            
            # Try to load settings
            settings_loaded = False
            settings_path_used = None
            settings_content = None
            
            for path in possible_paths:
                audit_log(f"load_settings: Checking path: {path} (exists: {os.path.exists(path)})", "CRITICAL")
                
                if os.path.exists(path):
                    audit_log(f"load_settings: Found settings file at: {path}", "CRITICAL")
                    
                    try:
                        # First read raw content
                        with open(path, 'r', encoding='utf-8') as f:
                            settings_content = f.read()
                        
                        audit_log(f"load_settings: Raw content from {path}: {settings_content}", "CRITICAL")
                        
                        # Now try to parse it
                        self.settings = json.loads(settings_content)
                        dump_dict(self.settings, f"self.settings from {path}")
                        
                        settings_loaded = True
                        settings_path_used = path
                        audit_log(f"load_settings: Successfully loaded settings from {path}", "CRITICAL")
                        break
                    except json.JSONDecodeError as je:
                        audit_log(f"load_settings: Failed to parse settings from {path}: {je}", "ERROR")
                        audit_log(f"load_settings: Invalid JSON content: {settings_content}", "ERROR")
                        continue
                    except Exception as e:
                        audit_log(f"load_settings: Error reading from {path}: {e}", "ERROR")
                        continue
            
            # If settings couldn't be loaded, create default
            if not settings_loaded:
                audit_log("load_settings: Could not load settings from any path, creating default", "CRITICAL")
                self.settings = {
                    'active_api': 'deepgram',
                    'apis': {
                        'deepgram': {'api_key': ''},
                        'openai': {'api_key': ''},
                        'groq': {'api_key': ''},
                        'elevenlabs': {'api_key': ''}
                    }
                }
                
                # Write default settings
                settings_path_used = 'settings.json'
                audit_log(f"load_settings: Writing default settings to {settings_path_used}", "CRITICAL")
                
                try:
                    with open(settings_path_used, 'w', encoding='utf-8') as f:
                        json.dump(self.settings, f, indent=2)
                    audit_log("load_settings: Default settings written successfully", "CRITICAL")
                except Exception as we:
                    audit_log(f"load_settings: Error writing default settings: {we}", "ERROR")
            
            # Extract active API from loaded settings
            active_api_from_settings = self.settings.get('active_api')
            audit_log(f"load_settings: Active API from loaded settings: '{active_api_from_settings}'", "CRITICAL")
            
            # Set active_api based on loaded settings
            if active_api_from_settings:
                old_api = self.active_api
                self.active_api = active_api_from_settings
                audit_log(f"load_settings: Updated active_api from '{old_api}' to '{self.active_api}'", "CRITICAL")
            else:
                audit_log(f"load_settings: No active_api in settings, keeping current: '{self.active_api}'", "CRITICAL")
                
                # If no active API set, default to deepgram
                if not self.active_api:
                    self.active_api = 'deepgram'
                    audit_log("load_settings: No active_api available, defaulted to 'deepgram'", "CRITICAL")
                    
                    # Update settings
                    self.settings['active_api'] = 'deepgram'
                    audit_log("load_settings: Updated settings with active_api = 'deepgram'", "CRITICAL")
                    
                    # Save updated settings
                    if settings_path_used:
                        try:
                            with open(settings_path_used, 'w', encoding='utf-8') as f:
                                json.dump(self.settings, f, indent=2)
                            audit_log("load_settings: Saved updated settings with default active_api", "CRITICAL")
                        except Exception as we:
                            audit_log(f"load_settings: Error saving updated settings: {we}", "ERROR")
            
            # Get API key
            apis = self.settings.get('apis', {})
            audit_log(f"load_settings: APIs in settings: {list(apis.keys())}", "CRITICAL")
            
            api_key = apis.get(self.active_api, {}).get('api_key', '')
            masked_key = api_key[:4] + "****" if len(api_key) > 4 else api_key
            audit_log(f"load_settings: Got API key for '{self.active_api}': {masked_key}", "CRITICAL")
            
            # Validate API key
            if not api_key.strip():
                audit_log(f"load_settings: No API key found for '{self.active_api}', client will be None", "CRITICAL")
                self.api_client = None
                raise Exception(f"API key is required for {self.active_api}")
            
            # Load settings into API client manager and get client
            audit_log(f"load_settings: Using API client manager for {self.active_api} client with key {masked_key}", "CRITICAL")

            # Load settings into the API client manager
            self.api_client_manager.load_settings()

            # Update API key in the manager
            self.api_client_manager.update_api_key(self.active_api, api_key)

            # Get client from manager (this will create it if needed)
            self.api_client = self.api_client_manager.get_client(self.active_api)

            if self.api_client:
                audit_log(f"load_settings: Got {self.active_api} client from manager: {log_obj_info(self.api_client, f'{self.active_api} client')}", "CRITICAL")
            else:
                audit_log(f"load_settings: Failed to get {self.active_api} client from manager", "CRITICAL")
                raise Exception(f"Failed to create {self.active_api} client")
            
            # Verify client type
            client_type_mapping = {
                'deepgram': DeepgramClient,
                'openai': OpenAI,
                'groq': Groq,
                'elevenlabs': ElevenLabs
            }
            expected_type = client_type_mapping.get(self.active_api)
            
            if not expected_type:
                audit_log(f"load_settings: ERROR - No expected type for '{self.active_api}'", "ERROR")
                raise Exception(f"Unknown API type: {self.active_api}")
            
            if not isinstance(self.api_client, expected_type):
                audit_log(f"load_settings: CRITICAL ERROR - Client type mismatch! Expected {expected_type.__name__}, got {type(self.api_client).__name__}", "ERROR")
                raise Exception(f"Client type mismatch: expected {expected_type.__name__}, got {type(self.api_client).__name__}")
            
            # Test API connection works (not just valid credentials format)
            connection_valid = self.validate_api_connection()
            if not connection_valid:
                audit_log(f"load_settings: API client created but connection test failed for {self.active_api}", "ERROR")
                # We'll keep the client but mark it as invalid
                if not hasattr(self, 'api_statuses'):
                    self.api_statuses = {}
                self.api_statuses[self.active_api] = {'valid': False}
                
                # Store API status in settings file
                try:
                    if 'api_statuses' not in self.settings:
                        self.settings['api_statuses'] = {}
                    self.settings['api_statuses'][self.active_api] = {'valid': False}
                    
                    settings_path = 'settings.json'
                    if os.path.exists(settings_path):
                        with open(settings_path, 'w', encoding='utf-8') as f:
                            json.dump(self.settings, f, indent=2)
                        audit_log(f"load_settings: Updated API status in settings.json", "INFO")
                except Exception as e:
                    audit_log(f"load_settings: Failed to update API status in settings: {e}", "ERROR")
            else:
                audit_log(f"load_settings: API connection test PASSED for {self.active_api}", "CRITICAL")
                
                # Store API status
                if not hasattr(self, 'api_statuses'):
                    self.api_statuses = {}
                self.api_statuses[self.active_api] = {'valid': True}
                
                # Store API status in settings file
                try:
                    if 'api_statuses' not in self.settings:
                        self.settings['api_statuses'] = {}
                    self.settings['api_statuses'][self.active_api] = {'valid': True}
                    
                    settings_path = 'settings.json'
                    if os.path.exists(settings_path):
                        with open(settings_path, 'w', encoding='utf-8') as f:
                            json.dump(self.settings, f, indent=2)
                        audit_log(f"load_settings: Updated API status in settings.json", "INFO")
                except Exception as e:
                    audit_log(f"load_settings: Failed to update API status in settings: {e}", "ERROR")
            
            audit_log(f"load_settings: SUCCESS - Created {self.active_api} client: {type(self.api_client).__name__}", "CRITICAL")
            return True
            
        except Exception as e:
            audit_log(f"load_settings: ERROR initializing API client: {e}", "ERROR")
            audit_log(f"load_settings: Traceback: {traceback.format_exc()}", "ERROR")
            self.api_client = None
            raise
        finally:
            audit_log("=====================================================", "CRITICAL")
            audit_log(f"LOAD_SETTINGS COMPLETED (active_api: {self.active_api}, client: {type(self.api_client).__name__ if self.api_client else 'None'})", "CRITICAL")
            audit_log("=====================================================", "CRITICAL")
    
    def paste_text(self, text):
        """Paste text to the active window using optimized clipboard method with safety checks."""
        if not text or not isinstance(text, str):
            debug_log("paste_text: No valid text to paste.", "WARNING")
            return

        try:
            # Check if it's safe to paste BEFORE setting clipboard for better performance
            if not self._is_safe_to_paste():
                debug_log("Not safe to paste - skipping automatic paste. Text is in clipboard for manual use.", "WARNING")
                # Still put text in clipboard for manual use
                self._set_clipboard_text(text)
                if self.app and hasattr(self.app, 'ui_manager') and self.app.ui_manager:
                    self.app.ui_manager.update_main_status_message("Text in clipboard - manual paste required", "WARNING")
                return

            # Put the text in the clipboard
            self._set_clipboard_text(text)
            debug_log(f"Text placed in clipboard: {text[:50]}...", "INFO")

            # Reduced delay for better performance - just enough for clipboard to be ready
            time.sleep(0.05)

            # Perform the paste using Ctrl+V with optimized timing
            debug_log("Pasting text using Ctrl+V...", "INFO")
            try:
                keyboard_controller = Controller()
                # Send Ctrl+V with minimal delays
                keyboard_controller.press(Key.ctrl)
                keyboard_controller.press('v')
                keyboard_controller.release('v')
                keyboard_controller.release(Key.ctrl)
                debug_log("Finished pasting text.", "INFO")
            except Exception as e_pynput:
                debug_log(f"Pynput error during pasting: {e_pynput}", "ERROR")
                if self.app and hasattr(self.app, 'ui_manager') and self.app.ui_manager:
                    self.app.ui_manager.update_main_status_message(f"Paste failed: {e_pynput}", "ERROR")
        except Exception as e_outer:
            debug_log(f"Outer error in paste_text: {e_outer}", "ERROR")

    def _set_clipboard_text(self, text):
        """Set text to Windows clipboard with optimized error handling."""
        max_retries = 3
        retry_delay = 0.01  # 10ms delay between retries

        for attempt in range(max_retries):
            try:
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()
                win32clipboard.SetClipboardText(text, win32clipboard.CF_UNICODETEXT)
                win32clipboard.CloseClipboard()
                return  # Success, exit early
            except Exception as e:
                if attempt < max_retries - 1:
                    debug_log(f"Clipboard attempt {attempt + 1} failed, retrying: {e}", "DEBUG")
                    time.sleep(retry_delay)
                    continue
                else:
                    debug_log(f"Error setting clipboard after {max_retries} attempts: {e}", "ERROR")
                    raise

    def _is_safe_to_paste(self):
        """Check if it's safe to paste based on the active window - optimized for performance."""
        try:
            # Get the currently active window
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                debug_log("No foreground window found", "WARNING")
                return False

            # Get class name first (faster than window title)
            class_name = win32gui.GetClassName(hwnd)

            # Quick check for unsafe window classes first
            unsafe_classes = {"CabinetWClass", "ExploreWClass", "Progman", "WorkerW",
                            "TaskManagerWindow", "RegEdit_RegEdit"}
            if class_name in unsafe_classes:
                debug_log(f"Active window is unsafe class ({class_name}) - not pasting", "INFO")
                return False

            # Only get window title if class check passes (optimization)
            window_title = win32gui.GetWindowText(hwnd)
            debug_log(f"Active window: '{window_title}' (class: {class_name})", "DEBUG")

            # Check if it's our own app (Whisp) - case insensitive
            window_title_lower = window_title.lower()
            if "whisp" in window_title_lower:
                debug_log("Active window is Whisp - not pasting", "INFO")
                return False

            # Quick check for other unsafe window titles
            unsafe_title_keywords = {"task manager", "control panel", "registry editor",
                                   "system configuration", "device manager"}
            if any(keyword in window_title_lower for keyword in unsafe_title_keywords):
                debug_log(f"Active window contains unsafe keyword - not pasting", "INFO")
                return False

            debug_log("Window appears safe for pasting", "INFO")
            return True

        except Exception as e:
            debug_log(f"Error checking window safety: {e}", "ERROR")
            return False

    # Keep the old type_text method for backward compatibility, but redirect to paste_text
    def type_text(self, text):
        """Legacy method - redirects to paste_text for better performance."""
        debug_log("type_text called - redirecting to paste_text method", "INFO")
        self.paste_text(text)
    
    async def transcribe_with_specific_api(self, audio_file, api_name):
        """Transcribe with a specific API without changing global state"""
        debug_log(f"Starting transcription of {audio_file} with SPECIFIC API: {api_name}")
        essential_log(f"🎤 Starting transcription with {api_name.upper()} API", "INFO")
        
        # Get API client from the manager (this will create it if needed)
        apis = self.settings.get('apis', {})
        api_key = apis.get(api_name, {}).get('api_key', '')

        if not api_key.strip():
            raise Exception(f"API key is required for {api_name}")

        # Update API key in manager and get client
        self.api_client_manager.update_api_key(api_name, api_key)
        temp_client = self.api_client_manager.get_client(api_name)

        if not temp_client:
            raise Exception(f"Failed to create {api_name} client")

        debug_log(f"Got client from manager for {api_name}: {type(temp_client).__name__}")
        
        # Call appropriate transcription method with temp client
        try:
            if api_name == 'deepgram':
                result = await self._transcribe_with_deepgram_client(audio_file, temp_client)
            elif api_name == 'openai':
                result = await self._transcribe_with_openai_client(audio_file, temp_client)
            elif api_name == 'groq':
                result = await self._transcribe_with_groq_client(audio_file, temp_client)
            elif api_name == 'elevenlabs':
                result = await self._transcribe_with_elevenlabs_client(audio_file, temp_client)
            else:
                raise Exception(f"Unsupported API: {api_name}")
            
            essential_log(f"✅ Transcription SUCCESS with {api_name.upper()}", "SUCCESS")
            essential_log(f"📝 Transcript: {result}", "INFO")
            return result
            
        except Exception as e:
            essential_log(f"❌ Transcription FAILED with {api_name.upper()}: {str(e)}", "ERROR")
            raise

    async def transcribe_audio(self, audio_file):
        """Transcribe the given audio file using the active API"""
        debug_log(f"Starting transcription of {audio_file} with {self.active_api}")
        
        # Log the active API being used (essential)
        essential_log(f"🎤 Starting transcription with {self.active_api.upper()} API", "INFO")
        
        # Update UI that we're starting transcription
        if hasattr(self.app, 'status_label'):
            self.app.root.after(0, lambda: self.app.status_label.configure(
                text="Starting transcription...",
                text_color="yellow"
            ))
        
        try:
            # Verify the audio file exists and has content
            if not os.path.exists(audio_file):
                error_msg = f"Audio file not found: {audio_file}"
                debug_log(error_msg)
                if hasattr(self.app, 'status_label'):
                    self.app.root.after(0, lambda: self.app.status_label.configure(
                        text=error_msg, 
                        text_color="red"
                    ))
                return None
                
            if os.path.getsize(audio_file) == 0:
                error_msg = f"Audio file is empty: {audio_file}"
                debug_log(error_msg)
                if hasattr(self.app, 'status_label'):
                    self.app.root.after(0, lambda: self.app.status_label.configure(
                        text=error_msg,
                        text_color="red"
                    ))
                return None
            
            # Check API client is available
            if not self.api_client:
                debug_log("No API client available, loading settings...")
                self.read_settings_file()
                self.load_settings()
                
                if not self.api_client:
                    error_msg = "Failed to initialize API client"
                    debug_log(error_msg)
                    if hasattr(self.app, 'status_label'):
                        self.app.root.after(0, lambda: self.app.status_label.configure(
                            text=error_msg,
                            text_color="red"
                        ))
                    return None
                    audit_log("transcribe_audio: API client is still None after reload attempt", "ERROR")
                    raise Exception("API not configured. Please check your API key in settings.")
            
            client_info = log_obj_info(self.api_client, "API client")
            audit_log(f"transcribe_audio: Using API: {self.active_api}, client type: {type(self.api_client).__name__}, ID: {id(self.api_client)}", "CRITICAL")
            
            # Log to UI
            if hasattr(self.app, 'ui_manager'):
                audit_log("transcribe_audio: Logging to UI", "INFO")
                debug_log(f"Starting transcription with {self.active_api} API for {audio_file}", "DEBUG")
            
            # Validate client type using the new method
            if not self.validate_api_client():
                audit_log("transcribe_audio: Client validation failed", "ERROR")
                if hasattr(self.app, 'ui_manager'):
                    debug_log(f"Starting transcription with {self.active_api} API for {audio_file}", "DEBUG")
                
                # Re-read settings file as a last resort
                audit_log("transcribe_audio: Re-reading settings file as a last resort", "CRITICAL")
                self.read_settings_file()
                self.load_settings()
                
                # Check again
                if not self.validate_api_client():
                    audit_log("transcribe_audio: CRITICAL - Client validation still failed after reload!", "ERROR")
                    raise Exception(f"Could not create correct API client type for {self.active_api}")
                else:
                    audit_log("transcribe_audio: Client validation succeeded after reload", "CRITICAL")
            
            # Call appropriate transcription method
            audit_log(f"transcribe_audio: Calling {self.active_api} transcription method", "CRITICAL")
            result = None
            
            if self.active_api == 'deepgram': 
                result = await self.transcribe_with_deepgram(audio_file)
            elif self.active_api == 'openai': 
                result = await self.transcribe_with_openai(audio_file)
            elif self.active_api == 'groq': 
                result = await self.transcribe_with_groq(audio_file)
            elif self.active_api == 'elevenlabs': 
                result = await self.transcribe_with_elevenlabs(audio_file)
            else:
                audit_log(f"transcribe_audio: UNSUPPORTED API: {self.active_api}", "ERROR")
                raise Exception(f"Unsupported API: {self.active_api}")
            
            # Log result
            if result:
                result_preview = result[:50] + "..." if len(result) > 50 else result
                audit_log(f"transcribe_audio: SUCCESS - Got transcript: {result_preview}", "CRITICAL")
                
                # Log essential transcription success and result
                essential_log(f"✅ Transcription SUCCESS with {self.active_api.upper()}", "SUCCESS")
                essential_log(f"📝 Transcript: {result}", "INFO")
                essential_log("", "INFO")  # Add separation line
            else:
                audit_log("transcribe_audio: WARNING - Empty transcript returned", "WARN")
                essential_log(f"⚠️ Transcription returned empty result from {self.active_api.upper()}", "WARNING")
                essential_log("", "INFO")  # Add separation line
            
            audit_log("=====================================================", "CRITICAL")
            audit_log("TRANSCRIBE_AUDIO COMPLETED SUCCESSFULLY", "CRITICAL")
            audit_log("=====================================================", "CRITICAL")
            
            return result
            
        except Exception as e:
            audit_log(f"transcribe_audio: ERROR: {e}", "ERROR")
            audit_log(f"transcribe_audio: Traceback: {traceback.format_exc()}", "ERROR")
            debug_log(f"Error during transcription: {str(e)}", "ERROR")
            
            # Log essential transcription failure
            essential_log(f"❌ Transcription FAILED with {self.active_api.upper()}: {str(e)}", "ERROR")
            essential_log("", "INFO")  # Add separation line
            
            audit_log("=====================================================", "CRITICAL")
            audit_log("TRANSCRIBE_AUDIO FAILED", "CRITICAL")
            audit_log("=====================================================", "CRITICAL")
            
            raise
            
    async def _transcribe_with_deepgram_client(self, audio_file, client):
        """Transcribe audio using specific Deepgram client"""
        debug_log(f"🔵 DEEPGRAM: Starting transcription of {audio_file}")
        
        try:
            # Deepgram v4 syntax - use FileSource payload
            with open(audio_file, 'rb') as audio:
                file_size = audio.seek(0, 2)  # Get file size
                audio.seek(0)  # Reset to beginning
                
                debug_log(f"🔵 DEEPGRAM: Audio file size: {file_size} bytes")
                
                payload = {
                    "buffer": audio
                }
                
                options = PrerecordedOptions(
                    model='nova-2',
                    punctuate=True,
                    language='en'
                )
                
                debug_log(f"🔵 DEEPGRAM: API Client Type: {type(client).__name__}")
                debug_log(f"🔵 DEEPGRAM: Request Options: model=nova-2, punctuate=True, language=en")
                debug_log("🔵 DEEPGRAM: Sending HTTP request to Deepgram API...")
                
                # Call API with passed client
                response = client.listen.rest.v("1").transcribe_file(
                    source=payload,
                    options=options
                )
                
                debug_log(f"🔵 DEEPGRAM: Response received, type: {type(response).__name__}")
                debug_log(f"🔵 DEEPGRAM: Response has 'results' attr: {hasattr(response, 'results')}")
            
            if not response or not hasattr(response, 'results'):
                error_msg = "Invalid response from Deepgram API"
                debug_log(f"🔵 DEEPGRAM: {error_msg}")
                raise Exception(error_msg)
            
            # Extract transcript
            transcript = response.results.channels[0].alternatives[0].transcript
            debug_log(f"🔵 DEEPGRAM: Raw transcript length: {len(transcript)} chars")
            debug_log(f"🔵 DEEPGRAM: Transcript first 100 chars: '{transcript[:100]}'")
            debug_log(f"🔵 DEEPGRAM: Transcript last 50 chars: '{transcript[-50:]}'")
            debug_log(f"🔵 DEEPGRAM: SUCCESS - Returning transcript")
            return transcript
                
        except Exception as e:
            error_msg = f"Deepgram error: {str(e)}"
            debug_log(f"🔵 DEEPGRAM: ERROR - {error_msg}")
            debug_log(f"🔵 DEEPGRAM: Error type: {type(e).__name__}")
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text=error_msg,
                    text_color="red"
                ))
            raise

    async def transcribe_with_deepgram(self, audio_file):
        """Transcribe audio using Deepgram API v4"""
        return await self._transcribe_with_deepgram_client(audio_file, self.api_client)
            
    async def _transcribe_with_openai_client(self, audio_file, client):
        """Transcribe audio using specific OpenAI client"""
        debug_log(f"🟢 OPENAI: Starting transcription of {audio_file}")
        
        try:
            with open(audio_file, 'rb') as audio:
                file_size = audio.seek(0, 2)  # Get file size
                audio.seek(0)  # Reset to beginning
                
                debug_log(f"🟢 OPENAI: Audio file size: {file_size} bytes")
                debug_log(f"🟢 OPENAI: API Client Type: {type(client).__name__}")
                debug_log(f"🟢 OPENAI: Request Model: whisper-1")
                debug_log("🟢 OPENAI: Sending HTTP request to OpenAI API...")
                
                transcription = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio
                )
            # File is automatically closed here by context manager
            
            # Force garbage collection to ensure file handle is released
            import gc
            gc.collect()
                
            debug_log(f"🟢 OPENAI: Response received, type: {type(transcription).__name__}")
            debug_log(f"🟢 OPENAI: Response has 'text' attr: {hasattr(transcription, 'text')}")
            
            if not transcription or not hasattr(transcription, 'text'):
                error_msg = "Invalid response from OpenAI API"
                debug_log(f"🟢 OPENAI: {error_msg}")
                raise Exception(error_msg)
                
            transcript = transcription.text
            debug_log(f"🟢 OPENAI: Raw transcript length: {len(transcript)} chars")
            debug_log(f"🟢 OPENAI: Transcript first 100 chars: '{transcript[:100]}'")
            debug_log(f"🟢 OPENAI: Transcript last 50 chars: '{transcript[-50:]}'")
            debug_log(f"🟢 OPENAI: SUCCESS - Returning transcript")
            return transcript
                
        except Exception as e:
            error_msg = f"OpenAI error: {str(e)}"
            debug_log(f"🟢 OPENAI: ERROR - {error_msg}")
            debug_log(f"🟢 OPENAI: Error type: {type(e).__name__}")
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text=error_msg,
                    text_color="red"
                ))
            raise

    async def transcribe_with_openai(self, audio_file):
        """Transcribe audio using OpenAI's Whisper API"""
        return await self._transcribe_with_openai_client(audio_file, self.api_client)
            
    async def _transcribe_with_groq_client(self, audio_file, client):
        """Transcribe audio using specific Groq client"""
        debug_log(f"🟡 GROQ: Starting transcription of {audio_file}")
        
        try:
            with open(audio_file, 'rb') as audio:
                file_size = audio.seek(0, 2)  # Get file size
                audio.seek(0)  # Reset to beginning
                
                debug_log(f"🟡 GROQ: Audio file size: {file_size} bytes")
                debug_log(f"🟡 GROQ: API Client Type: {type(client).__name__}")
                
                # Check if the client has the required attributes
                if not hasattr(client, 'audio') or not hasattr(client.audio, 'transcriptions'):
                    error_msg = "Groq client doesn't support audio transcription"
                    debug_log(f"🟡 GROQ: {error_msg}")
                    raise Exception(error_msg)
                
                debug_log(f"🟡 GROQ: Request Model: whisper-large-v3")
                debug_log("🟡 GROQ: Sending HTTP request to Groq API...")
                
                response = client.audio.transcriptions.create(
                    model="whisper-large-v3",
                    file=audio
                )
                
                debug_log(f"🟡 GROQ: Response received, type: {type(response).__name__}")
                debug_log(f"🟡 GROQ: Response has 'text' attr: {hasattr(response, 'text')}")
                
                if not hasattr(response, 'text'):
                    error_msg = "Invalid response from Groq API"
                    debug_log(f"🟡 GROQ: {error_msg}")
                    raise Exception(error_msg)
                    
                transcript = response.text
                debug_log(f"🟡 GROQ: Raw transcript length: {len(transcript)} chars")
                debug_log(f"🟡 GROQ: Transcript first 100 chars: '{transcript[:100]}'")
                debug_log(f"🟡 GROQ: Transcript last 50 chars: '{transcript[-50:]}'")
                debug_log(f"🟡 GROQ: SUCCESS - Returning transcript (after lstrip)")
                return transcript.lstrip()
                
        except Exception as e:
            error_msg = f"Groq error: {str(e)}"
            debug_log(f"🟡 GROQ: ERROR - {error_msg}")
            debug_log(f"🟡 GROQ: Error type: {type(e).__name__}")
            
            if "401" in str(e):
                error_msg = "Groq authentication error (401). Please check your API key."
                
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text=error_msg,
                    text_color="red"
                ))
            raise

    async def transcribe_with_groq(self, audio_file):
        """Transcribe audio using Groq's Whisper API"""
        return await self._transcribe_with_groq_client(audio_file, self.api_client)
            
    async def _transcribe_with_elevenlabs_client(self, audio_file, client):
        """Transcribe audio using specific ElevenLabs client"""
        debug_log(f"🟣 ELEVENLABS: Starting transcription of {audio_file}")
        
        try:
            # Check if the client has the required attributes
            if not hasattr(client, 'speech_to_text') or not hasattr(client.speech_to_text, 'convert'):
                error_msg = "ElevenLabs client doesn't support speech-to-text"
                debug_log(f"🟣 ELEVENLABS: {error_msg}")
                raise Exception(error_msg)
            
            with open(audio_file, 'rb') as audio:
                file_size = audio.seek(0, 2)  # Get file size
                audio.seek(0)  # Reset to beginning
                
                debug_log(f"🟣 ELEVENLABS: Audio file size: {file_size} bytes")
                debug_log(f"🟣 ELEVENLABS: API Client Type: {type(client).__name__}")
                debug_log(f"🟣 ELEVENLABS: Request Model: scribe_v1")
                debug_log("🟣 ELEVENLABS: Sending HTTP request to ElevenLabs API...")
                
                # Try with 'audio' parameter first, then fallback to 'file'
                try:
                    transcript = client.speech_to_text.convert(audio=audio, model_id="scribe_v1")
                    debug_log("🟣 ELEVENLABS: Used 'audio' parameter")
                except TypeError:
                    debug_log("🟣 ELEVENLABS: Falling back to 'file' parameter...")
                    audio.seek(0)  # Reset file pointer
                    transcript = client.speech_to_text.convert(file=audio, model_id="scribe_v1")
                    debug_log("🟣 ELEVENLABS: Used 'file' parameter")
                
                debug_log(f"🟣 ELEVENLABS: Response received, type: {type(transcript).__name__}")
                debug_log(f"🟣 ELEVENLABS: Response has 'text' attr: {hasattr(transcript, 'text')}")
                
                if not hasattr(transcript, 'text') or not transcript.text:
                    error_msg = "Empty or invalid response from ElevenLabs API"
                    debug_log(f"🟣 ELEVENLABS: {error_msg}")
                    raise Exception(error_msg)
                
                transcript_text = transcript.text
                debug_log(f"🟣 ELEVENLABS: Raw transcript length: {len(transcript_text)} chars")
                debug_log(f"🟣 ELEVENLABS: Transcript first 100 chars: '{transcript_text[:100]}'")
                debug_log(f"🟣 ELEVENLABS: Transcript last 50 chars: '{transcript_text[-50:]}'")
                debug_log(f"🟣 ELEVENLABS: SUCCESS - Returning transcript")
                return transcript_text
                
        except Exception as e:
            error_msg = f"ElevenLabs error: {str(e)}"
            debug_log(f"🟣 ELEVENLABS: ERROR - {error_msg}")
            debug_log(f"🟣 ELEVENLABS: Error type: {type(e).__name__}")
            
            if "401" in str(e):
                error_msg = "ElevenLabs authentication error (401). Please check your API key."
                
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text=error_msg,
                    text_color="red"
                ))
            raise

    async def transcribe_with_elevenlabs(self, audio_file):
        """Transcribe audio using ElevenLabs API"""
        return await self._transcribe_with_elevenlabs_client(audio_file, self.api_client)

    def validate_api_client(self):
        """Validate API client type matches active API and reload if necessary"""
        audit_log("=====================================================", "CRITICAL")
        audit_log("VALIDATE_API_CLIENT STARTED", "CRITICAL")
        audit_log(f"Current active_api: {self.active_api}", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
        
        try:
            # Define expected client types
            client_type_mapping = {
                'deepgram': DeepgramClient,
                'openai': OpenAI,
                'groq': Groq,
                'elevenlabs': ElevenLabs
            }
            
            # Get expected type for current API
            expected_type = client_type_mapping.get(self.active_api)
            if not expected_type:
                audit_log(f"Unknown API type: {self.active_api}", "ERROR")
                return False
                
            # Log current state
            if self.api_client is None:
                audit_log("Client is None, validation failed", "ERROR")
                return False
                
            # Check if client is correct type
            is_correct_type = isinstance(self.api_client, expected_type)
            audit_log(f"Client type validation: {is_correct_type} (expected {expected_type.__name__}, got {type(self.api_client).__name__})", "CRITICAL")
            
            # If wrong type, reload settings
            if not is_correct_type:
                audit_log("WRONG CLIENT TYPE! Forcing reload...", "CRITICAL")
                self.api_client = None  # Clear client
                self.load_settings()    # Reload with correct API
                
                # Check if fixed
                if not isinstance(self.api_client, expected_type):
                    audit_log("Failed to fix client type mismatch after reload!", "ERROR")
                    return False
                else:
                    audit_log("Successfully fixed client type", "CRITICAL")
                    return True
            else:
                audit_log("Client type is correct", "CRITICAL")
                return True
                
        except Exception as e:
            audit_log(f"Error validating client: {e}", "ERROR")
            audit_log(f"Traceback: {traceback.format_exc()}", "ERROR")
            return False
        finally:
            audit_log("=====================================================", "CRITICAL")
            audit_log("VALIDATE_API_CLIENT COMPLETED", "CRITICAL")
            audit_log("=====================================================", "CRITICAL") 

    def validate_api_connection(self, api_name=None, api_client=None):
        """Test the actual API connection to verify credentials work"""
        if api_name is None:
            api_name = self.active_api
        
        if api_client is None:
            api_client = self.api_client
            
        audit_log("=====================================================", "CRITICAL")
        audit_log(f"VALIDATING API CONNECTION FOR {api_name.upper()}", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")
        
        if api_client is None:
            audit_log(f"Cannot validate {api_name} - client is None", "ERROR")
            return False
            
        try:
            # Test different APIs using lightweight calls
            if api_name == 'deepgram':
                # For Deepgram, use the client directly without checking api_key attribute
                audit_log("Testing Deepgram connection...", "INFO")
                try:
                    # Check if client is properly structured
                    if not hasattr(api_client, 'transcription') or not hasattr(api_client.transcription, 'prerecorded'):
                        audit_log("Deepgram client lacks expected methods", "ERROR")
                        return False
                    
                    # Try to use the client to get projects
                    try:
                        response = api_client.get_projects()
                        audit_log("Deepgram API connection successful via projects API", "INFO")
                        return True
                    except Exception as api_err:
                        err_msg = str(api_err).lower()
                        if "401" in err_msg or "unauthorized" in err_msg:
                            audit_log(f"Deepgram authentication failed: {api_err}", "ERROR")
                            return False
                        # If we get here, the key might be valid but there's another error
                        audit_log(f"Deepgram API call error (possibly valid key): {api_err}", "INFO")
                        return True
                except Exception as e:
                    audit_log(f"Deepgram connection test failed: {e}", "ERROR")
                    return False
                
            elif api_name == 'openai':
                # For OpenAI, list models without checking api_key attribute
                audit_log("Testing OpenAI connection...", "INFO")
                try:
                    models = api_client.models.list()
                    if models and hasattr(models, 'data') and len(models.data) > 0:
                        audit_log(f"OpenAI connection successful, found {len(models.data)} models", "INFO")
                        return True
                    else:
                        audit_log("OpenAI connection failed - no models found", "ERROR")
                        return False
                except Exception as e:
                    err_msg = str(e).lower()
                    if "401" in err_msg or "unauthorized" in err_msg:
                        audit_log(f"OpenAI authentication failed: {e}", "ERROR")
                        return False
                    elif "rate limit" in err_msg:
                        audit_log(f"OpenAI API rate limited (valid key): {e}", "INFO")
                        return True
                    else:
                        audit_log(f"OpenAI connection error: {e}", "ERROR")
                        return False
                
            elif api_name == 'groq':
                # For Groq, test without checking api_key attribute
                audit_log("Testing Groq connection...", "INFO")
                try:
                    # Try models.list
                    if hasattr(api_client, 'models') and hasattr(api_client.models, 'list'):
                        try:
                            models = api_client.models.list()
                            if models and hasattr(models, 'data') and len(models.data) > 0:
                                audit_log(f"Groq connection successful, found {len(models.data)} models", "INFO")
                                return True
                            else:
                                audit_log("Groq models.list returned empty response", "ERROR")
                        except Exception as model_err:
                            err_msg = str(model_err).lower()
                            if "401" in err_msg or "unauthorized" in err_msg:
                                audit_log(f"Groq authentication failed: {model_err}", "ERROR")
                                return False
                            audit_log(f"Groq models.list error: {model_err}, trying chat completion", "INFO")
                
                    # Try chat completions as fallback
                    try:
                        response = api_client.chat.completions.create(
                            model="llama3-8b-8192",
                            messages=[{"role": "user", "content": "hi"}],
                            max_tokens=1
                        )
                        if hasattr(response, 'choices') and len(response.choices) > 0:
                            audit_log("Groq connection successful via chat completion", "INFO")
                            return True
                        audit_log("Groq chat completion returned invalid response", "ERROR")
                        return False
                    except Exception as chat_err:
                        err_msg = str(chat_err).lower()
                        if "401" in err_msg or "unauthorized" in err_msg:
                            audit_log(f"Groq authentication failed: {chat_err}", "ERROR")
                            return False
                        elif "rate limit" in err_msg:
                            audit_log("Groq rate limited (valid key)", "INFO")
                            return True
                        else:
                            audit_log(f"Groq chat completion error: {chat_err}", "ERROR")
                            return False
                except Exception as e:
                    audit_log(f"Groq connection test failed: {e}", "ERROR")
                    return False
                
            elif api_name == 'elevenlabs':
                # For ElevenLabs, test without checking api_key attribute
                audit_log("Testing ElevenLabs connection...", "INFO")
                try:
                    # Try user endpoint first
                    if hasattr(api_client, 'user') and hasattr(api_client.user, 'get'):
                        try:
                            user_info = api_client.user.get()
                            if user_info and hasattr(user_info, 'subscription'):
                                audit_log("ElevenLabs connection successful via user endpoint", "INFO")
                                return True
                            audit_log("ElevenLabs user.get returned invalid data", "ERROR")
                        except Exception as user_err:
                            err_msg = str(user_err).lower()
                            if "401" in err_msg or "unauthorized" in err_msg:
                                audit_log(f"ElevenLabs authentication failed: {user_err}", "ERROR")
                                return False
                            audit_log(f"ElevenLabs user endpoint error: {user_err}, trying models", "INFO")
                    
                    # Try models endpoint next
                    if hasattr(api_client, 'models') and hasattr(api_client.models, 'get'):
                        try:
                            models = api_client.models.get()
                            if models and len(models) > 0:
                                audit_log("ElevenLabs connection successful via models endpoint", "INFO")
                                return True
                            audit_log("ElevenLabs models.get returned empty response", "ERROR")
                            return False
                        except Exception as model_err:
                            err_msg = str(model_err).lower()
                            if "401" in err_msg or "unauthorized" in err_msg:
                                audit_log(f"ElevenLabs authentication failed: {model_err}", "ERROR")
                                return False
                            audit_log(f"ElevenLabs models endpoint error: {model_err}", "ERROR")
                            return False
                    
                    audit_log("ElevenLabs client lacks expected endpoints", "ERROR")
                    return False
                except Exception as e:
                    audit_log(f"ElevenLabs connection test failed: {e}", "ERROR")
                    return False
            
            # Unknown API
            audit_log(f"Unknown API: {api_name}", "ERROR")
            return False
            
        except Exception as e:
            audit_log(f"Error testing {api_name} connection: {e}", "ERROR")
            audit_log(f"Traceback: {traceback.format_exc()}", "ERROR")
            return False
        finally:
            audit_log("=====================================================", "CRITICAL")
            audit_log(f"API CONNECTION VALIDATION FOR {api_name.upper()} COMPLETED", "CRITICAL")
            audit_log("=====================================================", "CRITICAL")

    def reload_api_client(self):
        """Reload the API client by re-reading settings and reinitializing the client."""
        audit_log("=====================================================", "CRITICAL")
        audit_log("RELOAD_API_CLIENT STARTED", "CRITICAL")
        audit_log("=====================================================", "CRITICAL")

        try:
            # Store current API for comparison
            old_api = self.active_api
            old_client_type = type(self.api_client).__name__ if self.api_client else "None"

            audit_log(f"reload_api_client: Current state - API: {old_api}, Client: {old_client_type}", "CRITICAL")

            # Re-read settings file to get latest configuration
            self.read_settings_file()

            # Load settings and reinitialize client
            self.load_settings()

            # Log the result
            new_client_type = type(self.api_client).__name__ if self.api_client else "None"
            audit_log(f"reload_api_client: New state - API: {self.active_api}, Client: {new_client_type}", "CRITICAL")

            # Update app's API status if available
            if hasattr(self.app, 'update_api_status') and self.api_client:
                # Test the connection to determine validity
                is_valid = self.validate_api_connection()
                self.app.update_api_status(self.active_api, is_valid)
                audit_log(f"reload_api_client: Updated app API status for {self.active_api}: {is_valid}", "CRITICAL")

            audit_log("reload_api_client: SUCCESS", "CRITICAL")
            return True

        except Exception as e:
            audit_log(f"reload_api_client: ERROR - {e}", "ERROR")
            audit_log(f"reload_api_client: Traceback: {traceback.format_exc()}", "ERROR")

            # Ensure client is None on failure
            self.api_client = None

            # Update app's API status to invalid if available
            if hasattr(self.app, 'update_api_status'):
                self.app.update_api_status(self.active_api, False)
                audit_log(f"reload_api_client: Marked {self.active_api} as invalid due to error", "ERROR")

            return False

        finally:
            audit_log("=====================================================", "CRITICAL")
            audit_log("RELOAD_API_CLIENT COMPLETED", "CRITICAL")
            audit_log("=====================================================", "CRITICAL")