# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.knowledge_base_document_chunk_response_model import KnowledgeBaseDocumentChunkResponseModel
from .raw_client import Async<PERSON><PERSON><PERSON>hunk<PERSON>lient, RawChunkClient


class ChunkClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawChunkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawChunkClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawChunkClient
        """
        return self._raw_client

    def get(
        self, documentation_id: str, chunk_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> KnowledgeBaseDocumentChunkResponseModel:
        """
        Get details about a specific documentation part used by RAG.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        chunk_id : str
            The id of a document RAG chunk from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        KnowledgeBaseDocumentChunkResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.knowledge_base.documents.chunk.get(
            documentation_id="21m00Tcm4TlvDq8ikWAM",
            chunk_id="chunk_id",
        )
        """
        _response = self._raw_client.get(documentation_id, chunk_id, request_options=request_options)
        return _response.data


class AsyncChunkClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawChunkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawChunkClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawChunkClient
        """
        return self._raw_client

    async def get(
        self, documentation_id: str, chunk_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> KnowledgeBaseDocumentChunkResponseModel:
        """
        Get details about a specific documentation part used by RAG.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        chunk_id : str
            The id of a document RAG chunk from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        KnowledgeBaseDocumentChunkResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.knowledge_base.documents.chunk.get(
                documentation_id="21m00Tcm4TlvDq8ikWAM",
                chunk_id="chunk_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(documentation_id, chunk_id, request_options=request_options)
        return _response.data
