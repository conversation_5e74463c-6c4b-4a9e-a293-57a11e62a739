# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .. import core
from ..core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.audio_native_create_project_response_model import AudioNativeCreateProjectResponseModel
from ..types.audio_native_edit_content_response_model import AudioNativeEditContentResponseModel
from ..types.get_audio_native_project_settings_response_model import GetAudioNativeProjectSettingsResponseModel
from .raw_client import AsyncRawAudioNativeClient, RawAudioNativeClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class AudioNativeClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawAudioNativeClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawAudioNativeClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawAudioNativeClient
        """
        return self._raw_client

    def create(
        self,
        *,
        name: str,
        image: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        small: typing.Optional[bool] = OMIT,
        text_color: typing.Optional[str] = OMIT,
        background_color: typing.Optional[str] = OMIT,
        sessionization: typing.Optional[int] = OMIT,
        voice_id: typing.Optional[str] = OMIT,
        model_id: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AudioNativeCreateProjectResponseModel:
        """
        Creates Audio Native enabled project, optionally starts conversion and returns project ID and embeddable HTML snippet.

        Parameters
        ----------
        name : str
            Project name.

        image : typing.Optional[str]
            (Deprecated) Image URL used in the player. If not provided, default image set in the Player settings is used.

        author : typing.Optional[str]
            Author used in the player and inserted at the start of the uploaded article. If not provided, the default author set in the Player settings is used.

        title : typing.Optional[str]
            Title used in the player and inserted at the top of the uploaded article. If not provided, the default title set in the Player settings is used.

        small : typing.Optional[bool]
            (Deprecated) Whether to use small player or not. If not provided, default value set in the Player settings is used.

        text_color : typing.Optional[str]
            Text color used in the player. If not provided, default text color set in the Player settings is used.

        background_color : typing.Optional[str]
            Background color used in the player. If not provided, default background color set in the Player settings is used.

        sessionization : typing.Optional[int]
            (Deprecated) Specifies for how many minutes to persist the session across page reloads. If not provided, default sessionization set in the Player settings is used.

        voice_id : typing.Optional[str]
            Voice ID used to voice the content. If not provided, default voice ID set in the Player settings is used.

        model_id : typing.Optional[str]
            TTS Model ID used in the player. If not provided, default model ID set in the Player settings is used.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AudioNativeCreateProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.audio_native.create(
            name="name",
        )
        """
        _response = self._raw_client.create(
            name=name,
            image=image,
            author=author,
            title=title,
            small=small,
            text_color=text_color,
            background_color=background_color,
            sessionization=sessionization,
            voice_id=voice_id,
            model_id=model_id,
            file=file,
            auto_convert=auto_convert,
            request_options=request_options,
        )
        return _response.data

    def get_settings(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAudioNativeProjectSettingsResponseModel:
        """
        Get player settings for the specific project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAudioNativeProjectSettingsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.audio_native.get_settings(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get_settings(project_id, request_options=request_options)
        return _response.data

    def update(
        self,
        project_id: str,
        *,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_publish: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AudioNativeEditContentResponseModel:
        """
        Updates content for the specific AudioNative Project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        auto_publish : typing.Optional[bool]
            Whether to auto publish the new project snapshot after it's converted.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AudioNativeEditContentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.audio_native.update(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.update(
            project_id, file=file, auto_convert=auto_convert, auto_publish=auto_publish, request_options=request_options
        )
        return _response.data


class AsyncAudioNativeClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawAudioNativeClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawAudioNativeClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawAudioNativeClient
        """
        return self._raw_client

    async def create(
        self,
        *,
        name: str,
        image: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        small: typing.Optional[bool] = OMIT,
        text_color: typing.Optional[str] = OMIT,
        background_color: typing.Optional[str] = OMIT,
        sessionization: typing.Optional[int] = OMIT,
        voice_id: typing.Optional[str] = OMIT,
        model_id: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AudioNativeCreateProjectResponseModel:
        """
        Creates Audio Native enabled project, optionally starts conversion and returns project ID and embeddable HTML snippet.

        Parameters
        ----------
        name : str
            Project name.

        image : typing.Optional[str]
            (Deprecated) Image URL used in the player. If not provided, default image set in the Player settings is used.

        author : typing.Optional[str]
            Author used in the player and inserted at the start of the uploaded article. If not provided, the default author set in the Player settings is used.

        title : typing.Optional[str]
            Title used in the player and inserted at the top of the uploaded article. If not provided, the default title set in the Player settings is used.

        small : typing.Optional[bool]
            (Deprecated) Whether to use small player or not. If not provided, default value set in the Player settings is used.

        text_color : typing.Optional[str]
            Text color used in the player. If not provided, default text color set in the Player settings is used.

        background_color : typing.Optional[str]
            Background color used in the player. If not provided, default background color set in the Player settings is used.

        sessionization : typing.Optional[int]
            (Deprecated) Specifies for how many minutes to persist the session across page reloads. If not provided, default sessionization set in the Player settings is used.

        voice_id : typing.Optional[str]
            Voice ID used to voice the content. If not provided, default voice ID set in the Player settings is used.

        model_id : typing.Optional[str]
            TTS Model ID used in the player. If not provided, default model ID set in the Player settings is used.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AudioNativeCreateProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.audio_native.create(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            image=image,
            author=author,
            title=title,
            small=small,
            text_color=text_color,
            background_color=background_color,
            sessionization=sessionization,
            voice_id=voice_id,
            model_id=model_id,
            file=file,
            auto_convert=auto_convert,
            request_options=request_options,
        )
        return _response.data

    async def get_settings(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAudioNativeProjectSettingsResponseModel:
        """
        Get player settings for the specific project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAudioNativeProjectSettingsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.audio_native.get_settings(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_settings(project_id, request_options=request_options)
        return _response.data

    async def update(
        self,
        project_id: str,
        *,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_publish: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AudioNativeEditContentResponseModel:
        """
        Updates content for the specific AudioNative Project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        auto_publish : typing.Optional[bool]
            Whether to auto publish the new project snapshot after it's converted.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AudioNativeEditContentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.audio_native.update(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            project_id, file=file, auto_convert=auto_convert, auto_publish=auto_publish, request_options=request_options
        )
        return _response.data
