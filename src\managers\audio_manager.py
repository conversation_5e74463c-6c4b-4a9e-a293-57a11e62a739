import threading
import wave
import pyaudio
from playsound import playsound
import os
import sys
import traceback
import queue
import datetime
import tempfile

# Import numpy and pydub with error suppression to prevent CPU dispatcher conflicts
import warnings
warnings.filterwarnings('ignore', message='.*CPU dispatcher tracer already initialized.*')

# Global flags to track what's available
NUMPY_AVAILABLE = False
PYDUB_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except Exception as e:
    print(f"Warning: NumPy import issue: {e}")
    np = None

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except Exception as e:
    print(f"Warning: pydub import issue: {e}")
    AudioSegment = None

# Import debug_log from new location
from src.core.logging_utils import debug_log, essential_log

# Import error handling system
from src.core.error_handler import (
    get_error_handler, handle_error_with_context,
    ErrorSeverity, ErrorCategory
)

class AudioManager:
    def __init__(self, app):
        self.app = app
        # Amplitude processing queue and thread for non-blocking audio visualization
        self.amplitude_queue = queue.Queue(maxsize=50)
        self.amplitude_worker_active = False
        self.amplitude_worker_thread = None
        self.smoothed_amplitude = 0.1  # Shared smoothed amplitude value

        # Initialize error handler
        self.error_handler = get_error_handler(app)
        
    def get_resource_path(self, relative_path):
        """Get the correct path for resources in both development and PyInstaller environments"""
        try:
            # PyInstaller creates a temp folder and stores path in _MEIPASS
            base_path = getattr(sys, '_MEIPASS', os.path.abspath(os.path.dirname(__file__)))
            return os.path.join(base_path, relative_path)
        except Exception:
            return os.path.join(os.path.abspath(os.path.dirname(__file__)), relative_path)
        
    def play_sound(self, sound_file):
        """Play a sound file with error handling and recovery"""
        if not self.error_handler.is_error_boundary_active("audio_playback"):
            return  # Skip if audio playback is disabled due to repeated failures

        try:
            # Try multiple potential locations for the sound file
            possible_paths = [
                self.get_resource_path(sound_file),
                sound_file,
                os.path.join(os.path.dirname(os.path.abspath(__file__)), sound_file),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", os.path.basename(sound_file))
            ]

            # Try each path until one works
            for path in possible_paths:
                try:
                    if os.path.exists(path):
                        playsound(path)
                        return  # Exit if successful
                except Exception as e:
                    continue

            # If we got here, none of the paths worked
            raise FileNotFoundError(f"Sound file not found: {sound_file}")

        except Exception as e:
            # Use structured error handling
            handle_error_with_context(
                component="AudioManager",
                operation="play_sound",
                error=e,
                user_message="Audio notification unavailable",
                severity=ErrorSeverity.LOW,
                category=ErrorCategory.AUDIO,
                recoverable=False  # Sound playback failures are not critical
            )

    def _amplitude_processing_worker(self):
        """Background thread worker for processing audio amplitude calculations"""
        smoothing_factor = 0.7

        while self.amplitude_worker_active:
            try:
                # Get audio data from queue with timeout
                audio_data = self.amplitude_queue.get(timeout=0.1)

                # Calculate amplitude (moved from main recording thread)
                amplitude = 0.1  # Default in case of issues
                try:
                    if NUMPY_AVAILABLE and np is not None:
                        signal = np.frombuffer(audio_data, dtype=np.int16)
                        if len(signal) > 0:
                            # Calculate RMS amplitude, normalize, and scale
                            raw_amplitude_val = np.sqrt(np.mean(signal.astype(np.float32)**2)) / 32767.0
                            amplitude_val = raw_amplitude_val
                            amplitude = min(amplitude_val * 50, 1.0)  # Amplify for visual, clamp
                            amplitude = max(0.05, amplitude)  # Ensure a minimum visual nub
                        else:
                            amplitude = 0.05  # Default for empty signal
                    else:
                        # Fallback when NumPy is not available - use simple amplitude calculation
                        import struct
                        samples = [struct.unpack('<h', audio_data[i:i+2])[0] for i in range(0, len(audio_data), 2)]
                        if samples:
                            avg_amplitude = sum(abs(s) for s in samples) / len(samples) / 32767.0
                            amplitude = min(avg_amplitude * 50, 1.0)
                            amplitude = max(0.05, amplitude)
                        else:
                            amplitude = 0.05
                except Exception as e_np:
                    debug_log(f"[AUDIO] Audio processing error for viz: {e_np}", "WARN")
                    # Fallback to a random small value if processing fails
                    import random
                    amplitude = 0.05 + random.random() * 0.1

                # Apply smoothing to the amplitude
                self.smoothed_amplitude = (smoothing_factor * self.smoothed_amplitude) + ((1 - smoothing_factor) * amplitude)

                # Send the smoothed amplitude to the visualization
                if hasattr(self.app, 'ui_manager') and hasattr(self.app.ui_manager, 'bar_manager'):
                    try:
                        self.app.ui_manager.bar_manager.update_waveform_points(self.smoothed_amplitude)
                    except Exception as e:
                        debug_log(f"[AUDIO] Error sending amplitude to bar_manager: {e}", "WARN")

                # Also update the shared variable for any other components that might need it
                if hasattr(self.app, 'latest_amplitude_for_viz'):
                    self.app.latest_amplitude_for_viz = self.smoothed_amplitude

                # Mark task as done
                self.amplitude_queue.task_done()

            except queue.Empty:
                # Timeout occurred, continue loop
                continue
            except Exception as e:
                debug_log(f"[AUDIO] Error in amplitude processing worker: {e}", "ERROR")

    def _start_amplitude_worker(self):
        """Start the amplitude processing worker thread"""
        if not self.amplitude_worker_active:
            self.amplitude_worker_active = True
            self.amplitude_worker_thread = threading.Thread(target=self._amplitude_processing_worker, daemon=True)
            self.amplitude_worker_thread.start()
            debug_log("[AUDIO] Amplitude processing worker started", "INFO")

    def _stop_amplitude_worker(self):
        """Stop the amplitude processing worker thread"""
        if self.amplitude_worker_active:
            self.amplitude_worker_active = False
            if self.amplitude_worker_thread and self.amplitude_worker_thread.is_alive():
                self.amplitude_worker_thread.join(timeout=0.5)
            debug_log("[AUDIO] Amplitude processing worker stopped", "INFO")

    def _convert_wav_to_m4a_async(self, temp_wav_file, audio_file, callback):
        """Convert WAV to M4A in a background thread to avoid blocking UI"""
        def conversion_worker():
            try:
                if PYDUB_AVAILABLE and AudioSegment is not None:
                    debug_log("[AUDIO] Converting WAV to M4A format...", "INFO")
                    audio_segment = AudioSegment.from_wav(temp_wav_file)
                    # Export as M4A with good quality settings
                    audio_segment.export(audio_file, format="mp4", codec="aac", bitrate="128k")
                    debug_log("[AUDIO] Successfully converted to M4A format", "INFO")

                    # Clean up temporary WAV file
                    try:
                        os.remove(temp_wav_file)
                    except:
                        pass

                    # Call callback with success
                    callback(audio_file, None)
                else:
                    # Fallback: just use WAV format when pydub is not available
                    debug_log("[AUDIO] pydub not available, keeping WAV format", "INFO")
                    wav_file = audio_file.replace('.m4a', '.wav')
                    try:
                        os.rename(temp_wav_file, wav_file)
                        callback(wav_file, None)
                    except Exception as e:
                        callback(None, f"Failed to rename WAV file: {e}")

            except Exception as e:
                debug_log(f"[AUDIO] Error converting to M4A: {e}, keeping WAV format", "WARNING")
                # If conversion fails, rename temp WAV to final file
                wav_file = audio_file.replace('.m4a', '.wav')
                try:
                    os.rename(temp_wav_file, wav_file)
                    callback(wav_file, None)
                except Exception as rename_error:
                    callback(None, f"Conversion failed and WAV rename failed: {rename_error}")

        # Start conversion in background thread
        threading.Thread(target=conversion_worker, daemon=True).start()

    def _continue_with_transcription(self, audio_file, recording_start_time, fs, channels):
        """Continue with transcription after file conversion is complete"""
        try:
            # Calculate recording metadata
            recording_end_time = datetime.datetime.now()
            duration_seconds = (recording_end_time - recording_start_time).total_seconds()
            file_size = os.path.getsize(audio_file)

            # Log essential recording stats
            essential_log(f"📹 Recording completed: {duration_seconds:.1f}s, {file_size:,} bytes", "SUCCESS")

            # Store the recording with metadata using the audio storage manager
            stored_path = None
            if hasattr(self.app, 'audio_storage_manager'):
                metadata = {
                    'recorded_at': recording_start_time.isoformat(),
                    'duration_seconds': duration_seconds,
                    'sample_rate': fs,
                    'channels': channels,
                    'file_size_bytes': file_size
                }
                stored_path = self.app.audio_storage_manager.store_recording(audio_file, metadata)
                if stored_path:
                    debug_log(f"[AUDIO] Recording stored at: {stored_path}", "INFO")

            # Use stored path or fallback to original file
            transcription_file = stored_path or audio_file

            debug_log("[AUDIO] Recording saved, starting transcription...")

            # Start transcription in a new thread to handle async properly
            if hasattr(self.app, 'transcription_manager'):
                def start_transcription():
                    transcript = None # Initialize transcript to None
                    current_api = getattr(self.app.transcription_manager, 'active_api', 'unknown')

                    # Add transcription attempt to history
                    attempt_id = None
                    if hasattr(self.app, 'transcription_db'):
                        attempt_id = self.app.transcription_db.add_transcription_attempt(
                            audio_path=transcription_file,
                            api_used=current_api,
                            status='pending',
                            duration_seconds=duration_seconds,
                            file_size_bytes=file_size
                        )

                    try:
                        import asyncio
                        # Create a new event loop for this thread
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        # Run the async transcription and get the result
                        transcript = loop.run_until_complete(
                            self.app.transcription_manager.transcribe_audio(transcription_file)
                        )

                        # Update database with result
                        if attempt_id and hasattr(self.app, 'transcription_db'):
                            if transcript:
                                self.app.transcription_db.update_transcription_result(
                                    attempt_id, 'success', transcript=transcript
                                )
                            else:
                                self.app.transcription_db.update_transcription_result(
                                    attempt_id, 'failed', error_message="Transcription returned empty result"
                                )

                    except Exception as e:
                        error_msg = str(e)
                        debug_log(f"[AUDIO] Error in transcription processing: {e}")

                        # Update database with error
                        if attempt_id and hasattr(self.app, 'transcription_db'):
                            self.app.transcription_db.update_transcription_result(
                                attempt_id, 'failed', error_message=error_msg
                            )

                        self.app.root.after(0, self.app.ui_manager.update_status_label, f"Transcription error: {str(e)[:30]}...", "red")
                    finally:
                        if 'loop' in locals() and loop.is_running():
                            loop.close()
                        # Always attempt to update UI and reset recording state, regardless of transcription success
                        self.app.root.after(0, self.finalize_transcription_attempt, transcript)

                # Start the transcription in a new thread
                threading.Thread(target=start_transcription, daemon=True).start()
            else:
                # If no transcription manager, still finalize
                self.app.root.after(0, self.finalize_transcription_attempt, None)

        except Exception as e:
            debug_log(f"[AUDIO] Error in continue_with_transcription: {e}", "ERROR")
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text=f"Error processing recording: {str(e)[:50]}...",
                    text_color="red"
                ))

    def start_recording(self):
        self._start_amplitude_worker()  # Start amplitude processing worker
        threading.Thread(target=self.record_speech, daemon=True).start()
        
    def stop_recording(self):
        """Stop the current recording"""
        if hasattr(self.app, 'stop_recording'):
            self.app.stop_recording = True
        debug_log("[AUDIO] Stop recording requested", "INFO")
        
    def _get_recording_parameters(self):
        """Get audio recording parameters and file paths"""
        return {
            'chunk': 1024,
            'sample_format': pyaudio.paInt16,
            'channels': 1,
            'fs': 44100,
            'temp_wav_file': os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "recording_temp.wav"),
            'audio_file': os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "recording.m4a")
        }

    def _prepare_recording_environment(self, audio_file, temp_wav_file):
        """Prepare the recording environment by cleaning up old files"""
        # Update UI that we're starting
        if hasattr(self.app, 'status_label'):
            self.app.root.after(0, lambda: self.app.status_label.configure(
                text="Starting recording...",
                text_color="yellow"
            ))

        # Delete any existing recording files
        for file_to_delete in [audio_file, temp_wav_file]:
            if os.path.exists(file_to_delete):
                try:
                    os.remove(file_to_delete)
                except:
                    pass

    def _create_audio_stream(self, p, params):
        """Create and return the audio stream"""
        return p.open(
            format=params['sample_format'],
            channels=params['channels'],
            rate=params['fs'],
            frames_per_buffer=params['chunk'],
            input=True
        )

    def _record_audio_frames(self, stream, chunk):
        """Record audio frames until stop_recording is set"""
        frames = []
        while not self.app.stop_recording:
            try:
                data = stream.read(chunk, exception_on_overflow=False)
                frames.append(data)

                # Send audio data to amplitude processing worker (non-blocking)
                try:
                    self.amplitude_queue.put_nowait(data)
                except queue.Full:
                    # If queue is full, skip this frame's amplitude processing
                    # This prevents blocking the recording loop
                    pass
            except Exception as e:
                debug_log(f"[AUDIO] Error during recording: {e}")
                break
        return frames

    def _apply_audio_gain(self, frames, audio_gain=3.0):
        """Apply audio gain to recorded frames"""
        amplified_frames = []

        if NUMPY_AVAILABLE and np is not None:
            for frame in frames:
                # Convert to numpy array for processing
                audio_data = np.frombuffer(frame, dtype=np.int16)
                # Apply gain with clipping protection
                amplified_data = np.clip(audio_data.astype(np.float32) * audio_gain, -32767, 32767)
                # Convert back to int16
                amplified_frame = amplified_data.astype(np.int16).tobytes()
                amplified_frames.append(amplified_frame)

            debug_log(f"[AUDIO] Applied {audio_gain}x gain to recorded audio", "INFO")
        else:
            # Fallback without NumPy - apply gain using struct
            import struct
            for frame in frames:
                # Unpack audio data
                samples = [struct.unpack('<h', frame[i:i+2])[0] for i in range(0, len(frame), 2)]
                # Apply gain with clipping
                amplified_samples = [max(-32767, min(32767, int(sample * audio_gain))) for sample in samples]
                # Pack back to bytes
                amplified_frame = b''.join(struct.pack('<h', sample) for sample in amplified_samples)
                amplified_frames.append(amplified_frame)

            debug_log(f"[AUDIO] Applied {audio_gain}x gain to recorded audio (fallback mode)", "INFO")

        return amplified_frames

    def _save_wav_file(self, temp_wav_file, amplified_frames, params, p):
        """Save amplified frames to WAV file"""
        with wave.open(temp_wav_file, 'wb') as wf:
            wf.setnchannels(params['channels'])
            wf.setsampwidth(p.get_sample_size(params['sample_format']))
            wf.setframerate(params['fs'])
            wf.writeframes(b''.join(amplified_frames))

    def _process_and_save_recording(self, frames, params, p, recording_start_time):
        """Process recorded frames and save to file"""
        try:
            # Apply audio gain to the recorded frames
            amplified_frames = self._apply_audio_gain(frames)

            # Save as temporary WAV file
            self._save_wav_file(params['temp_wav_file'], amplified_frames, params, p)

            # Define callback for async conversion completion
            def on_conversion_complete(final_audio_file, error):
                if error:
                    debug_log(f"[AUDIO] Conversion error: {error}", "ERROR")
                    # Use temp WAV file as fallback
                    final_audio_file = params['temp_wav_file']

                # Continue with transcription process
                self._continue_with_transcription(final_audio_file, recording_start_time, params['fs'], params['channels'])

            # Start async conversion (non-blocking)
            self._convert_wav_to_m4a_async(params['temp_wav_file'], params['audio_file'], on_conversion_complete)

        except Exception as e:
            debug_log(f"[AUDIO] Error saving recording: {e}")
            if hasattr(self.app, 'status_label'):
                self.app.root.after(0, lambda: self.app.status_label.configure(
                    text=f"Error saving recording: {str(e)[:50]}...",
                    text_color="red"
                ))

    def _cleanup_recording_resources(self, stream, p):
        """Clean up recording resources and reset state"""
        # Stop amplitude processing worker
        self._stop_amplitude_worker()

        # Cleanup resources
        try:
            if stream and stream.is_active():
                stream.stop_stream()
            if stream:
                stream.close()
            p.terminate()
        except Exception as e:
            debug_log(f"[AUDIO] Error during cleanup: {e}")

        # Reset state
        self.app.stop_recording = False
        self.app.is_recording = False
        self.play_sound("assets/off.wav")
        debug_log("[AUDIO] Recording completed and cleaned up")

    def record_speech(self):
        """Main recording method - orchestrates the recording process with error boundaries"""
        if not self.error_handler.is_error_boundary_active("audio_recording"):
            debug_log("Audio recording disabled due to repeated failures", "WARNING")
            return

        params = self._get_recording_parameters()
        recording_start_time = datetime.datetime.now()

        # Initialize PyAudio
        p = pyaudio.PyAudio()
        stream = None

        self._prepare_recording_environment(params['audio_file'], params['temp_wav_file'])

        try:
            # Create audio stream
            stream = self._create_audio_stream(p, params)

            debug_log("[AUDIO] Starting recording...")
            self.play_sound("assets/on.wav")

            # Record audio frames
            frames = self._record_audio_frames(stream, params['chunk'])

            debug_log("[AUDIO] Recording stopped, saving file...")

            # Process and save the recording
            if frames:
                self._process_and_save_recording(frames, params, p, recording_start_time)

        except Exception as e:
            # Use structured error handling for recording failures
            recovery_success = handle_error_with_context(
                component="AudioManager",
                operation="record_speech",
                error=e,
                user_message="Recording failed. Please check your microphone and try again.",
                severity=ErrorSeverity.HIGH,
                category=ErrorCategory.AUDIO,
                recoverable=True
            )

            # If recovery failed multiple times, disable recording temporarily
            if not recovery_success:
                self.error_handler.disable_error_boundary("audio_recording")

        finally:
            self._cleanup_recording_resources(stream, p)

    def finalize_transcription_attempt(self, transcript):
        # This method is called from the transcription thread (via root.after)
        # after an attempt to transcribe audio.
        debug_log(f"Finalizing transcription attempt. Transcript (first 50 chars): '{str(transcript)[:50]}'", "AUDIO")
        
        try:
            if transcript and isinstance(transcript, str) and transcript.strip():
                # Success case - we have a valid transcript
                essential_log(f"✅ Transcription: {transcript}", "SUCCESS")
                self.app.handle_successful_transcription(transcript)
            else:
                # Failure case - no valid transcript
                error_message = "No transcript received or empty result"
                debug_log(f"Transcription failed: {error_message}", "AUDIO_ERROR")
                essential_log(f"❌ Transcription failed: {error_message}", "ERROR")
                self.app.handle_failed_transcription(error_message)
        except Exception as e:
            debug_log(f"Error in finalize_transcription_attempt: {e}", "AUDIO_ERROR")
            self.app.handle_failed_transcription(f"Internal error processing transcript: {e}")
        finally:
            # This resets the app's recording state variables.
            # UI reset (like button text, canvas prompt) is handled by display_transcription_result/error.
            if hasattr(self.app, 'reset_recording_ui_and_state'):
                self.app.reset_recording_ui_and_state()
            else:
                debug_log("CRITICAL: app has no reset_recording_ui_and_state method!", "AUDIO_ERROR")