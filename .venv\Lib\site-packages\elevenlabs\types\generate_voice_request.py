# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .body_generate_a_random_voice_v_1_voice_generation_generate_voice_post_age import (
    BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge,
)
from .body_generate_a_random_voice_v_1_voice_generation_generate_voice_post_gender import (
    BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender,
)


class GenerateVoiceRequest(UncheckedBaseModel):
    gender: BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender = pydantic.Field()
    """
    Category code corresponding to the gender of the generated voice. Possible values: female, male.
    """

    accent: str = pydantic.Field()
    """
    Category code corresponding to the accent of the generated voice. Possible values: british, american, african, australian, indian.
    """

    age: BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge = pydantic.Field()
    """
    Category code corresponding to the age of the generated voice. Possible values: young, middle_aged, old.
    """

    accent_strength: float = pydantic.Field()
    """
    The strength of the accent of the generated voice. Has to be between 0.3 and 2.0.
    """

    text: str = pydantic.Field()
    """
    Text to generate, text length has to be between 100 and 1000.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
