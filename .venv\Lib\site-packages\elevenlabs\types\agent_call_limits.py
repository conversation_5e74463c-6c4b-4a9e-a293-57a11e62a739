# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class AgentCallLimits(UncheckedBaseModel):
    agent_concurrency_limit: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum number of concurrent conversations. -1 indicates that there is no maximum
    """

    daily_limit: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum number of conversations per day
    """

    bursting_enabled: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to enable bursting. If true, exceeding workspace concurrency limit will be allowed up to 3 times the limit. Calls will be charged at double rate when exceeding the limit.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
