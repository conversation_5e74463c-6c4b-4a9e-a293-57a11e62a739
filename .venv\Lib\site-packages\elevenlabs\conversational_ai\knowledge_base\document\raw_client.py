# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ....core.api_error import ApiError
from ....core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ....core.http_response import AsyncHttpR<PERSON>ponse, HttpResponse
from ....core.jsonable_encoder import jsonable_encoder
from ....core.request_options import RequestOptions
from ....core.unchecked_base_model import construct_type
from ....errors.unprocessable_entity_error import UnprocessableEntityError
from ....types.embedding_model_enum import EmbeddingModelEnum
from ....types.http_validation_error import HttpValidationError
from ....types.rag_document_index_response_model import RagDocumentIndexResponseModel

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawDocumentClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def compute_rag_index(
        self,
        documentation_id: str,
        *,
        model: EmbeddingModelEnum,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[RagDocumentIndexResponseModel]:
        """
        In case the document is not RAG indexed, it triggers rag indexing task, otherwise it just returns the current status.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        model : EmbeddingModelEnum

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[RagDocumentIndexResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagDocumentIndexResponseModel,
                    construct_type(
                        type_=RagDocumentIndexResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawDocumentClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def compute_rag_index(
        self,
        documentation_id: str,
        *,
        model: EmbeddingModelEnum,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[RagDocumentIndexResponseModel]:
        """
        In case the document is not RAG indexed, it triggers rag indexing task, otherwise it just returns the current status.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        model : EmbeddingModelEnum

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[RagDocumentIndexResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "model": model,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagDocumentIndexResponseModel,
                    construct_type(
                        type_=RagDocumentIndexResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
