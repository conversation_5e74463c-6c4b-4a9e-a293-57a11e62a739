# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .prompt_agent_override_config import PromptAgentOverrideConfig


class AgentConfigOverrideConfig(UncheckedBaseModel):
    first_message: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to allow overriding the first_message field.
    """

    language: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to allow overriding the language field.
    """

    prompt: typing.Optional[PromptAgentOverrideConfig] = pydantic.Field(default=None)
    """
    Configures overrides for nested fields.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
