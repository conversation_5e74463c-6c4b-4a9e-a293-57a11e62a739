# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .llm_category_usage import LlmCategoryUsage


class ConversationChargingCommonModel(UncheckedBaseModel):
    dev_discount: typing.Optional[bool] = None
    is_burst: typing.Optional[bool] = None
    tier: typing.Optional[str] = None
    llm_usage: typing.Optional[LlmCategoryUsage] = None
    llm_price: typing.Optional[float] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
