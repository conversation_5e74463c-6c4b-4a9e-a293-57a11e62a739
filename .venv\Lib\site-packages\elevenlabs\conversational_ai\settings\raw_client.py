# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import Async<PERSON>tt<PERSON><PERSON><PERSON>ponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.serialization import convert_and_respect_annotation_metadata
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.conv_ai_webhooks import ConvAiWebhooks
from ...types.conversation_initiation_client_data_webhook import ConversationInitiationClientDataWebhook
from ...types.get_conv_ai_settings_response_model import GetConvAiSettingsResponseModel
from ...types.http_validation_error import HttpValidationError

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawSettingsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[GetConvAiSettingsResponseModel]:
        """
        Retrieve Convai settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetConvAiSettingsResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def update(
        self,
        *,
        conversation_initiation_client_data_webhook: typing.Optional[ConversationInitiationClientDataWebhook] = OMIT,
        webhooks: typing.Optional[ConvAiWebhooks] = OMIT,
        can_use_mcp_servers: typing.Optional[bool] = OMIT,
        rag_retention_period_days: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[GetConvAiSettingsResponseModel]:
        """
        Update Convai settings for the workspace

        Parameters
        ----------
        conversation_initiation_client_data_webhook : typing.Optional[ConversationInitiationClientDataWebhook]

        webhooks : typing.Optional[ConvAiWebhooks]

        can_use_mcp_servers : typing.Optional[bool]
            Whether the workspace can use MCP servers

        rag_retention_period_days : typing.Optional[int]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetConvAiSettingsResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "conversation_initiation_client_data_webhook": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data_webhook,
                    annotation=ConversationInitiationClientDataWebhook,
                    direction="write",
                ),
                "webhooks": convert_and_respect_annotation_metadata(
                    object_=webhooks, annotation=ConvAiWebhooks, direction="write"
                ),
                "can_use_mcp_servers": can_use_mcp_servers,
                "rag_retention_period_days": rag_retention_period_days,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawSettingsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[GetConvAiSettingsResponseModel]:
        """
        Retrieve Convai settings for the workspace

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetConvAiSettingsResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def update(
        self,
        *,
        conversation_initiation_client_data_webhook: typing.Optional[ConversationInitiationClientDataWebhook] = OMIT,
        webhooks: typing.Optional[ConvAiWebhooks] = OMIT,
        can_use_mcp_servers: typing.Optional[bool] = OMIT,
        rag_retention_period_days: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[GetConvAiSettingsResponseModel]:
        """
        Update Convai settings for the workspace

        Parameters
        ----------
        conversation_initiation_client_data_webhook : typing.Optional[ConversationInitiationClientDataWebhook]

        webhooks : typing.Optional[ConvAiWebhooks]

        can_use_mcp_servers : typing.Optional[bool]
            Whether the workspace can use MCP servers

        rag_retention_period_days : typing.Optional[int]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetConvAiSettingsResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/settings",
            base_url=self._client_wrapper.get_environment().base,
            method="PATCH",
            json={
                "conversation_initiation_client_data_webhook": convert_and_respect_annotation_metadata(
                    object_=conversation_initiation_client_data_webhook,
                    annotation=ConversationInitiationClientDataWebhook,
                    direction="write",
                ),
                "webhooks": convert_and_respect_annotation_metadata(
                    object_=webhooks, annotation=ConvAiWebhooks, direction="write"
                ),
                "can_use_mcp_servers": can_use_mcp_servers,
                "rag_retention_period_days": rag_retention_period_days,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetConvAiSettingsResponseModel,
                    construct_type(
                        type_=GetConvAiSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
