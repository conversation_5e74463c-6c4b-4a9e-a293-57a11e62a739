# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rapper, Sync<PERSON><PERSON>Wrapper
from ..core.request_options import RequestOptions
from .groups.client import AsyncGroupsClient, GroupsClient
from .invites.client import <PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON><PERSON>, Invites<PERSON>lient
from .members.client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Members<PERSON>lient
from .raw_client import AsyncRawWorkspaceClient, RawWorkspaceClient
from .resources.client import AsyncResources<PERSON>lient, ResourcesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class WorkspaceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawWorkspaceClient(client_wrapper=client_wrapper)
        self.groups = GroupsClient(client_wrapper=client_wrapper)

        self.invites = InvitesClient(client_wrapper=client_wrapper)

        self.members = MembersClient(client_wrapper=client_wrapper)

        self.resources = ResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawWorkspaceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawWorkspaceClient
        """
        return self._raw_client

    def update_user_auto_provisioning(
        self, *, enabled: bool, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Update user auto provisioning settings for the workspace.

        Parameters
        ----------
        enabled : bool

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.update_user_auto_provisioning(
            enabled=True,
        )
        """
        _response = self._raw_client.update_user_auto_provisioning(enabled=enabled, request_options=request_options)
        return _response.data


class AsyncWorkspaceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawWorkspaceClient(client_wrapper=client_wrapper)
        self.groups = AsyncGroupsClient(client_wrapper=client_wrapper)

        self.invites = AsyncInvitesClient(client_wrapper=client_wrapper)

        self.members = AsyncMembersClient(client_wrapper=client_wrapper)

        self.resources = AsyncResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawWorkspaceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawWorkspaceClient
        """
        return self._raw_client

    async def update_user_auto_provisioning(
        self, *, enabled: bool, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Update user auto provisioning settings for the workspace.

        Parameters
        ----------
        enabled : bool

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.update_user_auto_provisioning(
                enabled=True,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update_user_auto_provisioning(
            enabled=enabled, request_options=request_options
        )
        return _response.data
