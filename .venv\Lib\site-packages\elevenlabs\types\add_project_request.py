# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .body_add_project_v_1_projects_add_post_apply_text_normalization import (
    BodyAddProjectV1ProjectsAddPostApplyTextNormalization,
)
from .body_add_project_v_1_projects_add_post_fiction import BodyAddProjectV1ProjectsAddPostFiction
from .body_add_project_v_1_projects_add_post_source_type import BodyAddProjectV1ProjectsAddPostSourceType
from .body_add_project_v_1_projects_add_post_target_audience import BodyAddProjectV1ProjectsAddPostTargetAudience


class AddProjectRequest(UncheckedBaseModel):
    name: str = pydantic.Field()
    """
    The name of the Studio project, used for identification only.
    """

    default_title_voice_id: str = pydantic.Field()
    """
    The voice_id that corresponds to the default voice used for new titles.
    """

    default_paragraph_voice_id: str = pydantic.Field()
    """
    The voice_id that corresponds to the default voice used for new paragraphs.
    """

    default_model_id: str = pydantic.Field()
    """
    The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.
    """

    from_url: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' and 'from_content' must be null. If neither 'from_url', 'from_document', 'from_content' are provided we will initialize the Studio project as blank.
    """

    from_document: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional .epub, .pdf, .txt or similar file can be provided. If provided, we will initialize the Studio project with its content. If this is set, 'from_url' and 'from_content' must be null. If neither 'from_url', 'from_document', 'from_content' are provided we will initialize the Studio project as blank.
    """

    from_content_json: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional content to initialize the Studio project with. If this is set, 'from_url' and 'from_document' must be null. If neither 'from_url', 'from_document', 'from_content' are provided we will initialize the Studio project as blank.
    """

    quality_preset: typing.Optional[str] = pydantic.Field(default=None)
    """
    Output quality of the generated audio. Must be one of:
    standard - standard output format, 128kbps with 44.1kHz sample rate.
    high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
    ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
    ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.
    """

    title: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.
    """

    author: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.
    """

    description: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional description of the Studio project.
    """

    genres: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    An optional list of genres associated with the Studio project.
    """

    target_audience: typing.Optional[BodyAddProjectV1ProjectsAddPostTargetAudience] = pydantic.Field(default=None)
    """
    An optional target audience of the Studio project.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional language of the Studio project. Two-letter language code (ISO 639-1).
    """

    content_type: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional content type of the Studio project.
    """

    original_publication_date: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.
    """

    mature_content: typing.Optional[bool] = pydantic.Field(default=None)
    """
    An optional specification of whether this Studio project contains mature content.
    """

    isbn_number: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.
    """

    acx_volume_normalization: typing.Optional[bool] = pydantic.Field(default=None)
    """
    [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements
    """

    volume_normalization: typing.Optional[bool] = pydantic.Field(default=None)
    """
    When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements
    """

    pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.
    """

    callback_url: typing.Optional[str] = pydantic.Field(default=None)
    """
    
        A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion
        Messages:
        1. When project was converted successfully:
        {
          type: "project_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            conversion_status: "success",
            project_snapshot_id: "22m00Tcm4TlvDq8ikMAT",
            error_details: None,
          }
        }
        2. When project conversion failed:
        {
          type: "project_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            conversion_status: "error",
            project_snapshot_id: None,
            error_details: "Error details if conversion failed"
          }
        }
    
        3. When chapter was converted successfully:
        {
          type: "chapter_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            chapter_id: "22m00Tcm4TlvDq8ikMAT",
            conversion_status: "success",
            chapter_snapshot_id: "23m00Tcm4TlvDq8ikMAV",
            error_details: None,
          }
        }
        4. When chapter conversion failed:
        {
          type: "chapter_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            chapter_id: "22m00Tcm4TlvDq8ikMAT",
            conversion_status: "error",
            chapter_snapshot_id: None,
            error_details: "Error details if conversion failed"
          }
        }
        
    """

    fiction: typing.Optional[BodyAddProjectV1ProjectsAddPostFiction] = pydantic.Field(default=None)
    """
    An optional specification of whether the content of this Studio project is fiction.
    """

    apply_text_normalization: typing.Optional[BodyAddProjectV1ProjectsAddPostApplyTextNormalization] = pydantic.Field(
        default=None
    )
    """
    
        This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
        When set to 'auto', the system will automatically decide whether to apply text normalization
        (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
        with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.
        
    """

    auto_convert: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to auto convert the Studio project to audio or not.
    """

    auto_assign_voices: typing.Optional[bool] = pydantic.Field(default=None)
    """
    [Alpha Feature] Whether automatically assign voices to phrases in the create Project.
    """

    source_type: typing.Optional[BodyAddProjectV1ProjectsAddPostSourceType] = pydantic.Field(default=None)
    """
    The type of Studio project to create.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
