# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_history_transcript_common_model_output_role import ConversationHistoryTranscriptCommonModelOutputRole
from .conversation_history_transcript_common_model_output_source_medium import (
    ConversationHistoryTranscriptCommonModelOutputSourceMedium,
)
from .conversation_history_transcript_tool_call_common_model import ConversationHistoryTranscriptToolCallCommonModel
from .conversation_history_transcript_tool_result_common_model import ConversationHistoryTranscriptToolResultCommonModel
from .conversation_turn_metrics import ConversationTurnMetrics
from .llm_usage_output import LlmUsageOutput
from .rag_retrieval_info import RagRetrievalInfo
from .user_feedback import UserFeedback


class ConversationHistoryTranscriptCommonModelOutput(UncheckedBaseModel):
    role: ConversationHistoryTranscriptCommonModelOutputRole
    message: typing.Optional[str] = None
    tool_calls: typing.Optional[typing.List[ConversationHistoryTranscriptToolCallCommonModel]] = None
    tool_results: typing.Optional[typing.List[ConversationHistoryTranscriptToolResultCommonModel]] = None
    feedback: typing.Optional[UserFeedback] = None
    llm_override: typing.Optional[str] = None
    time_in_call_secs: int
    conversation_turn_metrics: typing.Optional[ConversationTurnMetrics] = None
    rag_retrieval_info: typing.Optional[RagRetrievalInfo] = None
    llm_usage: typing.Optional[LlmUsageOutput] = None
    interrupted: typing.Optional[bool] = None
    original_message: typing.Optional[str] = None
    source_medium: typing.Optional[ConversationHistoryTranscriptCommonModelOutputSourceMedium] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
