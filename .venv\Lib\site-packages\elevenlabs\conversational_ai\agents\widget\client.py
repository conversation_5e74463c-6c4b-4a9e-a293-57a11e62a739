# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.get_agent_embed_response_model import GetAgentEmbedResponseModel
from .avatar.client import AsyncA<PERSON>ar<PERSON><PERSON>, AvatarClient
from .raw_client import AsyncRawWidgetClient, RawWidgetClient


class WidgetClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawWidgetClient(client_wrapper=client_wrapper)
        self.avatar = AvatarClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawWidgetClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawWidgetClient
        """
        return self._raw_client

    def get(
        self,
        agent_id: str,
        *,
        conversation_signature: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentEmbedResponseModel:
        """
        Retrieve the widget configuration for an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        conversation_signature : typing.Optional[str]
            An expiring token that enables a websocket conversation to start. These can be generated for an agent using the /v1/convai/conversation/get-signed-url endpoint

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentEmbedResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.agents.widget.get(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(
            agent_id, conversation_signature=conversation_signature, request_options=request_options
        )
        return _response.data


class AsyncWidgetClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawWidgetClient(client_wrapper=client_wrapper)
        self.avatar = AsyncAvatarClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawWidgetClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawWidgetClient
        """
        return self._raw_client

    async def get(
        self,
        agent_id: str,
        *,
        conversation_signature: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetAgentEmbedResponseModel:
        """
        Retrieve the widget configuration for an agent

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        conversation_signature : typing.Optional[str]
            An expiring token that enables a websocket conversation to start. These can be generated for an agent using the /v1/convai/conversation/get-signed-url endpoint

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentEmbedResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.agents.widget.get(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(
            agent_id, conversation_signature=conversation_signature, request_options=request_options
        )
        return _response.data
