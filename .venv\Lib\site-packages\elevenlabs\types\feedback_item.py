# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class FeedbackItem(UncheckedBaseModel):
    thumbs_up: bool = pydantic.Field()
    """
    Whether the user liked the generated item.
    """

    feedback: str = pydantic.Field()
    """
    The feedback text provided by the user.
    """

    emotions: bool = pydantic.Field()
    """
    Whether the user provided emotions.
    """

    inaccurate_clone: bool = pydantic.Field()
    """
    Whether the user thinks the clone is inaccurate.
    """

    glitches: bool = pydantic.Field()
    """
    Whether the user thinks there are glitches in the audio.
    """

    audio_quality: bool = pydantic.Field()
    """
    Whether the user thinks the audio quality is good.
    """

    other: bool = pydantic.Field()
    """
    Whether the user provided other feedback.
    """

    review_status: typing.Optional[str] = pydantic.Field(default=None)
    """
    The review status of the item. Defaults to 'not_reviewed'.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
