# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class CreatePreviouslyGeneratedVoiceRequest(UncheckedBaseModel):
    voice_name: str = pydantic.Field()
    """
    Name to use for the created voice.
    """

    voice_description: str = pydantic.Field()
    """
    Description to use for the created voice.
    """

    generated_voice_id: str = pydantic.Field()
    """
    The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.
    """

    played_not_selected_voice_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    List of voice ids that the user has played but not selected. Used for RLHF.
    """

    labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = pydantic.Field(default=None)
    """
    Optional, metadata to add to the created voice. Defaults to None.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
