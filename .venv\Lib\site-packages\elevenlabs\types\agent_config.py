# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .dynamic_variables_config import DynamicVariablesConfig
from .prompt_agent_db_model import PromptAgentDbModel


class AgentConfig(UncheckedBaseModel):
    first_message: typing.Optional[str] = pydantic.Field(default=None)
    """
    If non-empty, the first message the agent will say. If empty, the agent waits for the user to start the discussion.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    Language of the agent - used for ASR and TTS
    """

    dynamic_variables: typing.Optional[DynamicVariablesConfig] = pydantic.Field(default=None)
    """
    Configuration for dynamic variables
    """

    prompt: typing.Optional[PromptAgentDbModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(AgentConfig)
