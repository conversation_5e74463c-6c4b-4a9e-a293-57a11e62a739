# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .. import core
from ..core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from .raw_client import AsyncRawAudioIsolationClient, RawAudioIsolationClient
from .types.audio_isolation_convert_request_file_format import AudioIsolationConvertRequestFileFormat
from .types.audio_isolation_stream_request_file_format import AudioIsolationStreamRequestFileFormat

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class AudioIsolationClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawAudioIsolationClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawAudioIsolationClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawAudioIsolationClient
        """
        return self._raw_client

    def convert(
        self,
        *,
        audio: core.File,
        file_format: typing.Optional[AudioIsolationConvertRequestFileFormat] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Removes background noise from audio.

        Parameters
        ----------
        audio : core.File
            See core.File for more documentation

        file_format : typing.Optional[AudioIsolationConvertRequestFileFormat]
            The format of input audio. Options are 'pcm_s16le_16' or 'other' For `pcm_s16le_16`, the input audio must be 16-bit PCM at a 16kHz sample rate, single channel (mono), and little-endian byte order. Latency will be lower than with passing an encoded waveform.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            Successful Response
        """
        with self._raw_client.convert(audio=audio, file_format=file_format, request_options=request_options) as r:
            yield from r.data

    def stream(
        self,
        *,
        audio: core.File,
        file_format: typing.Optional[AudioIsolationStreamRequestFileFormat] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Removes background noise from audio.

        Parameters
        ----------
        audio : core.File
            See core.File for more documentation

        file_format : typing.Optional[AudioIsolationStreamRequestFileFormat]
            The format of input audio. Options are 'pcm_s16le_16' or 'other' For `pcm_s16le_16`, the input audio must be 16-bit PCM at a 16kHz sample rate, single channel (mono), and little-endian byte order. Latency will be lower than with passing an encoded waveform.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            Successful Response
        """
        with self._raw_client.stream(audio=audio, file_format=file_format, request_options=request_options) as r:
            yield from r.data


class AsyncAudioIsolationClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawAudioIsolationClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawAudioIsolationClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawAudioIsolationClient
        """
        return self._raw_client

    async def convert(
        self,
        *,
        audio: core.File,
        file_format: typing.Optional[AudioIsolationConvertRequestFileFormat] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Removes background noise from audio.

        Parameters
        ----------
        audio : core.File
            See core.File for more documentation

        file_format : typing.Optional[AudioIsolationConvertRequestFileFormat]
            The format of input audio. Options are 'pcm_s16le_16' or 'other' For `pcm_s16le_16`, the input audio must be 16-bit PCM at a 16kHz sample rate, single channel (mono), and little-endian byte order. Latency will be lower than with passing an encoded waveform.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            Successful Response
        """
        async with self._raw_client.convert(audio=audio, file_format=file_format, request_options=request_options) as r:
            async for _chunk in r.data:
                yield _chunk

    async def stream(
        self,
        *,
        audio: core.File,
        file_format: typing.Optional[AudioIsolationStreamRequestFileFormat] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Removes background noise from audio.

        Parameters
        ----------
        audio : core.File
            See core.File for more documentation

        file_format : typing.Optional[AudioIsolationStreamRequestFileFormat]
            The format of input audio. Options are 'pcm_s16le_16' or 'other' For `pcm_s16le_16`, the input audio must be 16-bit PCM at a 16kHz sample rate, single channel (mono), and little-endian byte order. Latency will be lower than with passing an encoded waveform.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            Successful Response
        """
        async with self._raw_client.stream(audio=audio, file_format=file_format, request_options=request_options) as r:
            async for _chunk in r.data:
                yield _chunk
