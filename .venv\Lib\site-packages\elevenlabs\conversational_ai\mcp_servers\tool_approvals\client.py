# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.mcp_server_response_model import McpServerResponseModel
from ....types.mcp_tool_approval_policy import McpToolApprovalPolicy
from .raw_client import AsyncRawToolA<PERSON>rovalsClient, RawToolApprovalsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ToolApprovalsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawToolApprovalsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawToolApprovalsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawToolApprovalsClient
        """
        return self._raw_client

    def create(
        self,
        mcp_server_id: str,
        *,
        tool_name: str,
        tool_description: str,
        input_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        approval_policy: typing.Optional[McpToolApprovalPolicy] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> McpServerResponseModel:
        """
        Add approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            The name of the MCP tool

        tool_description : str
            The description of the MCP tool

        input_schema : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The input schema of the MCP tool (the schema defined on the MCP server before ElevenLabs does any extra processing)

        approval_policy : typing.Optional[McpToolApprovalPolicy]
            The tool-level approval policy

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.tool_approvals.create(
            mcp_server_id="mcp_server_id",
            tool_name="tool_name",
            tool_description="tool_description",
        )
        """
        _response = self._raw_client.create(
            mcp_server_id,
            tool_name=tool_name,
            tool_description=tool_description,
            input_schema=input_schema,
            approval_policy=approval_policy,
            request_options=request_options,
        )
        return _response.data

    def delete(
        self, mcp_server_id: str, tool_name: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> McpServerResponseModel:
        """
        Remove approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            Name of the MCP tool to remove approval for.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.tool_approvals.delete(
            mcp_server_id="mcp_server_id",
            tool_name="tool_name",
        )
        """
        _response = self._raw_client.delete(mcp_server_id, tool_name, request_options=request_options)
        return _response.data


class AsyncToolApprovalsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawToolApprovalsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawToolApprovalsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawToolApprovalsClient
        """
        return self._raw_client

    async def create(
        self,
        mcp_server_id: str,
        *,
        tool_name: str,
        tool_description: str,
        input_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        approval_policy: typing.Optional[McpToolApprovalPolicy] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> McpServerResponseModel:
        """
        Add approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            The name of the MCP tool

        tool_description : str
            The description of the MCP tool

        input_schema : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The input schema of the MCP tool (the schema defined on the MCP server before ElevenLabs does any extra processing)

        approval_policy : typing.Optional[McpToolApprovalPolicy]
            The tool-level approval policy

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.tool_approvals.create(
                mcp_server_id="mcp_server_id",
                tool_name="tool_name",
                tool_description="tool_description",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            mcp_server_id,
            tool_name=tool_name,
            tool_description=tool_description,
            input_schema=input_schema,
            approval_policy=approval_policy,
            request_options=request_options,
        )
        return _response.data

    async def delete(
        self, mcp_server_id: str, tool_name: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> McpServerResponseModel:
        """
        Remove approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            Name of the MCP tool to remove approval for.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.tool_approvals.delete(
                mcp_server_id="mcp_server_id",
                tool_name="tool_name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(mcp_server_id, tool_name, request_options=request_options)
        return _response.data
