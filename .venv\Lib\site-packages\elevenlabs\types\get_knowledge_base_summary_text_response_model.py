# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .document_usage_mode_enum import DocumentUsageModeEnum
from .get_knowledge_base_summary_text_response_model_dependent_agents_item import (
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem,
)
from .knowledge_base_document_metadata_response_model import KnowledgeBaseDocumentMetadataResponseModel
from .resource_access_info import ResourceAccessInfo


class GetKnowledgeBaseSummaryTextResponseModel(UncheckedBaseModel):
    id: str
    name: str
    metadata: KnowledgeBaseDocumentMetadataResponseModel
    supported_usages: typing.List[DocumentUsageModeEnum]
    access_info: ResourceAccessInfo
    dependent_agents: typing.List[GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
