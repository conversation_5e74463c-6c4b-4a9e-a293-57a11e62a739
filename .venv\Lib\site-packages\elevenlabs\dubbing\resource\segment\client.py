# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.segment_delete_response import SegmentDeleteResponse
from ....types.segment_update_response import SegmentUpdateR<PERSON>ponse
from .raw_client import AsyncRawSegmentClient, RawSegmentClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SegmentClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSegmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSegmentClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSegmentClient
        """
        return self._raw_client

    def update(
        self,
        dubbing_id: str,
        segment_id: str,
        language: str,
        *,
        start_time: typing.Optional[float] = OMIT,
        end_time: typing.Optional[float] = OMIT,
        text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentUpdateResponse:
        """
        Modifies a single segment with new text and/or start/end times. Will update the values for only a specific language of a segment. Does not automatically regenerate the dub.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        language : str
            ID of the language.

        start_time : typing.Optional[float]

        end_time : typing.Optional[float]

        text : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentUpdateResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.segment.update(
            dubbing_id="dubbing_id",
            segment_id="segment_id",
            language="language",
        )
        """
        _response = self._raw_client.update(
            dubbing_id,
            segment_id,
            language,
            start_time=start_time,
            end_time=end_time,
            text=text,
            request_options=request_options,
        )
        return _response.data

    def delete(
        self, dubbing_id: str, segment_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SegmentDeleteResponse:
        """
        Deletes a single segment from the dubbing.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDeleteResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.segment.delete(
            dubbing_id="dubbing_id",
            segment_id="segment_id",
        )
        """
        _response = self._raw_client.delete(dubbing_id, segment_id, request_options=request_options)
        return _response.data


class AsyncSegmentClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSegmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSegmentClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSegmentClient
        """
        return self._raw_client

    async def update(
        self,
        dubbing_id: str,
        segment_id: str,
        language: str,
        *,
        start_time: typing.Optional[float] = OMIT,
        end_time: typing.Optional[float] = OMIT,
        text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentUpdateResponse:
        """
        Modifies a single segment with new text and/or start/end times. Will update the values for only a specific language of a segment. Does not automatically regenerate the dub.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        language : str
            ID of the language.

        start_time : typing.Optional[float]

        end_time : typing.Optional[float]

        text : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentUpdateResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.segment.update(
                dubbing_id="dubbing_id",
                segment_id="segment_id",
                language="language",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            dubbing_id,
            segment_id,
            language,
            start_time=start_time,
            end_time=end_time,
            text=text,
            request_options=request_options,
        )
        return _response.data

    async def delete(
        self, dubbing_id: str, segment_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SegmentDeleteResponse:
        """
        Deletes a single segment from the dubbing.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segment_id : str
            ID of the segment

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDeleteResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.segment.delete(
                dubbing_id="dubbing_id",
                segment_id="segment_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(dubbing_id, segment_id, request_options=request_options)
        return _response.data
