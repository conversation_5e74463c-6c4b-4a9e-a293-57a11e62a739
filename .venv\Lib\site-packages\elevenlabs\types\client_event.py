# This file was auto-generated by Fern from our API Definition.

import typing

ClientEvent = typing.Union[
    typing.Literal[
        "conversation_initiation_metadata",
        "asr_initiation_metadata",
        "ping",
        "audio",
        "interruption",
        "user_transcript",
        "agent_response",
        "agent_response_correction",
        "client_tool_call",
        "mcp_tool_call",
        "mcp_connection_status",
        "agent_tool_response",
        "vad_score",
        "internal_turn_probability",
        "internal_tentative_agent_response",
    ],
    typing.Any,
]
