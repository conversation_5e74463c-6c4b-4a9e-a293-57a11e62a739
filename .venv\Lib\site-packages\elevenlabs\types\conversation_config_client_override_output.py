# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_config_override import AgentConfigOverride
from .conversation_config_override import ConversationConfigOverride
from .tts_conversational_config_override import TtsConversationalConfigOverride


class ConversationConfigClientOverrideOutput(UncheckedBaseModel):
    tts: typing.Optional[TtsConversationalConfigOverride] = pydantic.Field(default=None)
    """
    Configuration for conversational text to speech
    """

    conversation: typing.Optional[ConversationConfigOverride] = pydantic.Field(default=None)
    """
    Configuration for conversational events
    """

    agent: typing.Optional[AgentConfigOverride] = pydantic.Field(default=None)
    """
    Agent specific configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
