# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import os
import typing

import httpx
from .audio_isolation.client import AsyncAudio<PERSON>solationClient, AudioIsolationClient
from .audio_native.client import AsyncAudioNativeClient, AudioNativeClient
from .conversational_ai.client import AsyncConversationalAiClient, ConversationalAiClient
from .core.client_wrapper import As<PERSON><PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from .dubbing.client import AsyncDubbing<PERSON>lient, DubbingClient
from .environment import ElevenLabsEnvironment
from .forced_alignment.client import AsyncForcedAlignmentClient, ForcedAlignmentClient
from .history.client import AsyncHistoryClient, HistoryClient
from .models.client import AsyncModelsClient, ModelsClient
from .pronunciation_dictionaries.client import AsyncPronunciationDictionariesClient, PronunciationDictionariesClient
from .samples.client import AsyncSamplesClient, SamplesClient
from .speech_to_speech.client import AsyncSpeechToSpeechClient, SpeechToSpeechClient
from .speech_to_text.client import AsyncSpeechToTextClient, SpeechToTextClient
from .studio.client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Studio<PERSON>lient
from .text_to_dialogue.client import AsyncTextToD<PERSON>ogue<PERSON>lient, TextToDialogueClient
from .text_to_sound_effects.client import AsyncTextToSoundEffectsClient, TextToSoundEffectsClient
from .text_to_speech.client import AsyncTextToSpeechClient, TextToSpeechClient
from .text_to_voice.client import AsyncTextToVoiceClient, TextToVoiceClient
from .usage.client import AsyncUsageClient, UsageClient
from .user.client import AsyncUserClient, UserClient
from .voices.client import AsyncVoicesClient, VoicesClient
from .webhooks.client import AsyncWebhooksClient, WebhooksClient
from .workspace.client import AsyncWorkspaceClient, WorkspaceClient


class BaseElevenLabs:
    """
    Use this class to access the different functions within the SDK. You can instantiate any number of clients with different configuration that will propagate to these functions.

    Parameters
    ----------
    environment : ElevenLabsEnvironment
        The environment to use for requests from the client. from .environment import ElevenLabsEnvironment



        Defaults to ElevenLabsEnvironment.PRODUCTION



    api_key : typing.Optional[str]
    timeout : typing.Optional[float]
        The timeout to be used, in seconds, for requests. By default the timeout is 240 seconds, unless a custom httpx client is used, in which case this default is not enforced.

    follow_redirects : typing.Optional[bool]
        Whether the default httpx client follows redirects or not, this is irrelevant if a custom httpx client is passed in.

    httpx_client : typing.Optional[httpx.Client]
        The httpx client to use for making requests, a preconfigured client is used by default, however this is useful should you want to pass in any custom httpx configuration.

    Examples
    --------
    from elevenlabs import ElevenLabs

    client = ElevenLabs(
        api_key="YOUR_API_KEY",
    )
    """

    def __init__(
        self,
        *,
        environment: ElevenLabsEnvironment = ElevenLabsEnvironment.PRODUCTION,
        api_key: typing.Optional[str] = os.getenv("ELEVENLABS_API_KEY"),
        timeout: typing.Optional[float] = None,
        follow_redirects: typing.Optional[bool] = True,
        httpx_client: typing.Optional[httpx.Client] = None,
    ):
        _defaulted_timeout = (
            timeout if timeout is not None else 240 if httpx_client is None else httpx_client.timeout.read
        )
        self._client_wrapper = SyncClientWrapper(
            environment=environment,
            api_key=api_key,
            httpx_client=httpx_client
            if httpx_client is not None
            else httpx.Client(timeout=_defaulted_timeout, follow_redirects=follow_redirects)
            if follow_redirects is not None
            else httpx.Client(timeout=_defaulted_timeout),
            timeout=_defaulted_timeout,
        )
        self.history = HistoryClient(client_wrapper=self._client_wrapper)
        self.text_to_sound_effects = TextToSoundEffectsClient(client_wrapper=self._client_wrapper)
        self.audio_isolation = AudioIsolationClient(client_wrapper=self._client_wrapper)
        self.samples = SamplesClient(client_wrapper=self._client_wrapper)
        self.text_to_speech = TextToSpeechClient(client_wrapper=self._client_wrapper)
        self.text_to_dialogue = TextToDialogueClient(client_wrapper=self._client_wrapper)
        self.speech_to_speech = SpeechToSpeechClient(client_wrapper=self._client_wrapper)
        self.text_to_voice = TextToVoiceClient(client_wrapper=self._client_wrapper)
        self.user = UserClient(client_wrapper=self._client_wrapper)
        self.voices = VoicesClient(client_wrapper=self._client_wrapper)
        self.studio = StudioClient(client_wrapper=self._client_wrapper)
        self.dubbing = DubbingClient(client_wrapper=self._client_wrapper)
        self.models = ModelsClient(client_wrapper=self._client_wrapper)
        self.audio_native = AudioNativeClient(client_wrapper=self._client_wrapper)
        self.usage = UsageClient(client_wrapper=self._client_wrapper)
        self.pronunciation_dictionaries = PronunciationDictionariesClient(client_wrapper=self._client_wrapper)
        self.workspace = WorkspaceClient(client_wrapper=self._client_wrapper)
        self.webhooks = WebhooksClient(client_wrapper=self._client_wrapper)
        self.speech_to_text = SpeechToTextClient(client_wrapper=self._client_wrapper)
        self.forced_alignment = ForcedAlignmentClient(client_wrapper=self._client_wrapper)
        self.conversational_ai = ConversationalAiClient(client_wrapper=self._client_wrapper)


class AsyncBaseElevenLabs:
    """
    Use this class to access the different functions within the SDK. You can instantiate any number of clients with different configuration that will propagate to these functions.

    Parameters
    ----------
    environment : ElevenLabsEnvironment
        The environment to use for requests from the client. from .environment import ElevenLabsEnvironment



        Defaults to ElevenLabsEnvironment.PRODUCTION



    api_key : typing.Optional[str]
    timeout : typing.Optional[float]
        The timeout to be used, in seconds, for requests. By default the timeout is 240 seconds, unless a custom httpx client is used, in which case this default is not enforced.

    follow_redirects : typing.Optional[bool]
        Whether the default httpx client follows redirects or not, this is irrelevant if a custom httpx client is passed in.

    httpx_client : typing.Optional[httpx.AsyncClient]
        The httpx client to use for making requests, a preconfigured client is used by default, however this is useful should you want to pass in any custom httpx configuration.

    Examples
    --------
    from elevenlabs import AsyncElevenLabs

    client = AsyncElevenLabs(
        api_key="YOUR_API_KEY",
    )
    """

    def __init__(
        self,
        *,
        environment: ElevenLabsEnvironment = ElevenLabsEnvironment.PRODUCTION,
        api_key: typing.Optional[str] = os.getenv("ELEVENLABS_API_KEY"),
        timeout: typing.Optional[float] = None,
        follow_redirects: typing.Optional[bool] = True,
        httpx_client: typing.Optional[httpx.AsyncClient] = None,
    ):
        _defaulted_timeout = (
            timeout if timeout is not None else 240 if httpx_client is None else httpx_client.timeout.read
        )
        self._client_wrapper = AsyncClientWrapper(
            environment=environment,
            api_key=api_key,
            httpx_client=httpx_client
            if httpx_client is not None
            else httpx.AsyncClient(timeout=_defaulted_timeout, follow_redirects=follow_redirects)
            if follow_redirects is not None
            else httpx.AsyncClient(timeout=_defaulted_timeout),
            timeout=_defaulted_timeout,
        )
        self.history = AsyncHistoryClient(client_wrapper=self._client_wrapper)
        self.text_to_sound_effects = AsyncTextToSoundEffectsClient(client_wrapper=self._client_wrapper)
        self.audio_isolation = AsyncAudioIsolationClient(client_wrapper=self._client_wrapper)
        self.samples = AsyncSamplesClient(client_wrapper=self._client_wrapper)
        self.text_to_speech = AsyncTextToSpeechClient(client_wrapper=self._client_wrapper)
        self.text_to_dialogue = AsyncTextToDialogueClient(client_wrapper=self._client_wrapper)
        self.speech_to_speech = AsyncSpeechToSpeechClient(client_wrapper=self._client_wrapper)
        self.text_to_voice = AsyncTextToVoiceClient(client_wrapper=self._client_wrapper)
        self.user = AsyncUserClient(client_wrapper=self._client_wrapper)
        self.voices = AsyncVoicesClient(client_wrapper=self._client_wrapper)
        self.studio = AsyncStudioClient(client_wrapper=self._client_wrapper)
        self.dubbing = AsyncDubbingClient(client_wrapper=self._client_wrapper)
        self.models = AsyncModelsClient(client_wrapper=self._client_wrapper)
        self.audio_native = AsyncAudioNativeClient(client_wrapper=self._client_wrapper)
        self.usage = AsyncUsageClient(client_wrapper=self._client_wrapper)
        self.pronunciation_dictionaries = AsyncPronunciationDictionariesClient(client_wrapper=self._client_wrapper)
        self.workspace = AsyncWorkspaceClient(client_wrapper=self._client_wrapper)
        self.webhooks = AsyncWebhooksClient(client_wrapper=self._client_wrapper)
        self.speech_to_text = AsyncSpeechToTextClient(client_wrapper=self._client_wrapper)
        self.forced_alignment = AsyncForcedAlignmentClient(client_wrapper=self._client_wrapper)
        self.conversational_ai = AsyncConversationalAiClient(client_wrapper=self._client_wrapper)
