"""
Transcription History Manager for Whisp2
Manages transcription history, results, and resubmission capabilities.
"""

import os
import json
import datetime
import asyncio
from typing import List, Dict, Any, Optional
from src.core.logging_utils import debug_log


class TranscriptionHistoryManager:
  """Manages transcription history and provides resubmission capabilities."""
  
  def __init__(self, app_instance):
    self.app = app_instance
    self.history_file = self._get_history_file_path()
    self.history = self._load_history()
    
  def _get_history_file_path(self) -> str:
    """Get the path to the transcription history file."""
    app_dir = self.app._get_app_directory()
    return os.path.join(app_dir, 'transcription_history.json')
  
  def _load_history(self) -> List[Dict[str, Any]]:
    """Load transcription history from file."""
    try:
      if os.path.exists(self.history_file):
        with open(self.history_file, 'r', encoding='utf-8') as f:
          data = json.load(f)
          if isinstance(data, list):
            return data
          else:
            debug_log("Invalid history file format, starting fresh", "WARNING")
            return []
      else:
        debug_log("No history file found, starting fresh", "INFO")
        return []
    except Exception as e:
      debug_log(f"Error loading transcription history: {e}", "ERROR")
      return []
  
  def _save_history(self) -> None:
    """Save transcription history to file."""
    try:
      # Keep only last 1000 entries to prevent file from growing too large
      if len(self.history) > 1000:
        self.history = self.history[:1000]
      
      with open(self.history_file, 'w', encoding='utf-8') as f:
        json.dump(self.history, f, indent=2, ensure_ascii=False)
        
    except Exception as e:
      debug_log(f"Error saving transcription history: {e}", "ERROR")
  
  def add_transcription_attempt(self, 
                               audio_path: str, 
                               api_used: str, 
                               status: str,
                               transcript: Optional[str] = None,
                               error_message: Optional[str] = None,
                               duration_seconds: Optional[float] = None,
                               file_size_bytes: Optional[int] = None) -> str:
    """
    Add a new transcription attempt to history.
    
    Args:
      audio_path: Path to the audio file
      api_used: API that was used for transcription
      status: Status ('success', 'failed', 'pending')
      transcript: The transcript text if successful
      error_message: Error message if failed
      duration_seconds: Duration of the audio in seconds
      file_size_bytes: Size of the audio file in bytes
      
    Returns:
      Unique ID for this transcription attempt
    """
    try:
      # Generate unique ID
      timestamp = datetime.datetime.now()
      attempt_id = timestamp.strftime("%Y%m%d_%H%M%S_%f")[:-3]
      
      # Get file info if not provided
      if file_size_bytes is None and os.path.exists(audio_path):
        file_size_bytes = os.path.getsize(audio_path)
      
      entry = {
        'id': attempt_id,
        'timestamp': timestamp.isoformat(),
        'audio_path': audio_path,
        'audio_filename': os.path.basename(audio_path),
        'api_used': api_used,
        'status': status,
        'transcript': transcript,
        'error_message': error_message,
        'duration_seconds': duration_seconds,
        'file_size_bytes': file_size_bytes,
        'file_size_mb': round(file_size_bytes / (1024 * 1024), 2) if file_size_bytes else None
      }
      
      # Add to beginning of history (newest first)
      self.history.insert(0, entry)
      
      # Save to file
      self._save_history()
      
      debug_log(f"Added transcription attempt to history: {attempt_id}", "INFO")
      return attempt_id
      
    except Exception as e:
      debug_log(f"Error adding transcription attempt: {e}", "ERROR")
      return ""
  
  def update_transcription_result(self, 
                                 attempt_id: str, 
                                 status: str,
                                 transcript: Optional[str] = None,
                                 error_message: Optional[str] = None) -> bool:
    """
    Update the result of a transcription attempt.
    
    Args:
      attempt_id: ID of the transcription attempt
      status: New status ('success', 'failed')
      transcript: The transcript text if successful
      error_message: Error message if failed
      
    Returns:
      True if update was successful, False otherwise
    """
    try:
      for entry in self.history:
        if entry.get('id') == attempt_id:
          entry['status'] = status
          entry['updated_at'] = datetime.datetime.now().isoformat()
          
          if transcript:
            entry['transcript'] = transcript
          if error_message:
            entry['error_message'] = error_message
          
          self._save_history()
          debug_log(f"Updated transcription result: {attempt_id}", "INFO")
          return True
      
      debug_log(f"Transcription attempt not found: {attempt_id}", "WARNING")
      return False
      
    except Exception as e:
      debug_log(f"Error updating transcription result: {e}", "ERROR")
      return False
  
  def get_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Get transcription history.
    
    Args:
      limit: Maximum number of entries to return
      
    Returns:
      List of transcription history entries
    """
    if limit is None:
      return self.history.copy()
    else:
      return self.history[:limit]
  
  def get_by_id(self, attempt_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific transcription attempt by ID."""
    for entry in self.history:
      if entry.get('id') == attempt_id:
        return entry.copy()
    return None
  
  def get_failed_transcriptions(self) -> List[Dict[str, Any]]:
    """Get all failed transcription attempts."""
    return [entry for entry in self.history if entry.get('status') == 'failed']
  
  def delete_entry(self, attempt_id: str) -> bool:
    """
    Delete a transcription history entry.
    
    Args:
      attempt_id: ID of the entry to delete
      
    Returns:
      True if deletion was successful, False otherwise
    """
    try:
      original_length = len(self.history)
      self.history = [entry for entry in self.history if entry.get('id') != attempt_id]
      
      if len(self.history) < original_length:
        self._save_history()
        debug_log(f"Deleted transcription history entry: {attempt_id}", "INFO")
        return True
      else:
        debug_log(f"Transcription history entry not found: {attempt_id}", "WARNING")
        return False
        
    except Exception as e:
      debug_log(f"Error deleting transcription history entry: {e}", "ERROR")
      return False
  
  async def resubmit_transcription(self, attempt_id: str, new_api: Optional[str] = None) -> bool:
    """
    Resubmit a transcription with the same or different API.
    
    Args:
      attempt_id: ID of the original transcription attempt
      new_api: API to use for resubmission (if None, uses original API)
      
    Returns:
      True if resubmission was initiated successfully, False otherwise
    """
    try:
      # Find the original entry
      original_entry = self.get_by_id(attempt_id)
      if not original_entry:
        debug_log(f"Original transcription attempt not found: {attempt_id}", "ERROR")
        return False
      
      audio_path = original_entry.get('audio_path')
      if not audio_path or not os.path.exists(audio_path):
        debug_log(f"Audio file not found for resubmission: {audio_path}", "ERROR")
        return False
      
      # Determine which API to use
      api_to_use = new_api or original_entry.get('api_used')
      if not api_to_use:
        debug_log("No API specified for resubmission", "ERROR")
        return False
      
      # Create new transcription attempt entry
      new_attempt_id = self.add_transcription_attempt(
        audio_path=audio_path,
        api_used=api_to_use,
        status='pending',
        duration_seconds=original_entry.get('duration_seconds'),
        file_size_bytes=original_entry.get('file_size_bytes')
      )
      
      # Update the transcription manager's active API if needed
      if hasattr(self.app, 'transcription_manager'):
        # Store original API to restore later
        original_api = getattr(self.app.transcription_manager, 'active_api', None)
        debug_log(f"Switching API from {original_api} to {api_to_use} for resubmission", "INFO")
        
        try:
          # Set the new API WITHOUT calling load_settings() which would reset it
          self.app.transcription_manager.active_api = api_to_use
          
          # Manually initialize the API client for the new API
          if hasattr(self.app, 'settings_manager'):
            apis = self.app.settings_manager.get('apis', {})
            api_config = apis.get(api_to_use, {})
            api_key = api_config.get('api_key', '')
            
            if not api_key:
              raise Exception(f"No API key configured for {api_to_use}")
            
            # Use API client manager to get the client
            api_client_manager = self.app.transcription_manager.api_client_manager
            api_client_manager.update_api_key(api_to_use, api_key)
            self.app.transcription_manager.api_client = api_client_manager.get_client(api_to_use)

            if not self.app.transcription_manager.api_client:
              raise Exception(f"Failed to create {api_to_use} client")
            
            debug_log(f"Initialized {api_to_use} client for resubmission", "INFO")
          else:
            raise Exception("Settings manager not available")
          
          # Perform the transcription
          transcript = await self.app.transcription_manager.transcribe_audio(audio_path)
          
          if transcript:
            # Update with success
            self.update_transcription_result(new_attempt_id, 'success', transcript=transcript)
            debug_log(f"Resubmission successful: {new_attempt_id}", "INFO")
            return True
          else:
            # Update with failure
            self.update_transcription_result(new_attempt_id, 'failed', error_message="Transcription returned empty result")
            debug_log(f"Resubmission failed: {new_attempt_id}", "ERROR")
            return False
            
        except Exception as transcription_error:
          # Update with failure
          error_msg = str(transcription_error)
          self.update_transcription_result(new_attempt_id, 'failed', error_message=error_msg)
          debug_log(f"Resubmission failed with error: {error_msg}", "ERROR")
          return False
          
        finally:
          # Restore original API and client
          if original_api:
            debug_log(f"Restoring original API: {original_api}", "INFO")
            self.app.transcription_manager.active_api = original_api
            # Re-initialize the original client
            try:
              if hasattr(self.app, 'settings_manager'):
                apis = self.app.settings_manager.get('apis', {})
                api_config = apis.get(original_api, {})
                api_key = api_config.get('api_key', '')
                
                if api_key:
                  # Use API client manager to restore the client
                  api_client_manager = self.app.transcription_manager.api_client_manager
                  api_client_manager.update_api_key(original_api, api_key)
                  self.app.transcription_manager.api_client = api_client_manager.get_client(original_api)

                  debug_log(f"Restored {original_api} client using API client manager", "INFO")
            except Exception as restore_error:
              debug_log(f"Error restoring original API client: {restore_error}", "WARNING")
              # Fallback: use load_settings to restore
              self.app.transcription_manager.load_settings()
      
      else:
        debug_log("Transcription manager not available for resubmission", "ERROR")
        return False
        
    except Exception as e:
      debug_log(f"Error during transcription resubmission: {e}", "ERROR")
      return False
  
  def cleanup_old_entries(self, days_to_keep: int = 90) -> int:
    """
    Clean up old history entries.
    
    Args:
      days_to_keep: Number of days of history to keep
      
    Returns:
      Number of entries deleted
    """
    try:
      cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
      original_count = len(self.history)
      
      self.history = [
        entry for entry in self.history 
        if datetime.datetime.fromisoformat(entry['timestamp']) > cutoff_date
      ]
      
      deleted_count = original_count - len(self.history)
      
      if deleted_count > 0:
        self._save_history()
        debug_log(f"Cleaned up {deleted_count} old transcription history entries", "INFO")
      
      return deleted_count
      
    except Exception as e:
      debug_log(f"Error cleaning up transcription history: {e}", "ERROR")
      return 0
  
  def get_statistics(self) -> Dict[str, Any]:
    """Get statistics about transcription history."""
    try:
      if not self.history:
        return {}
      
      total_attempts = len(self.history)
      successful = len([e for e in self.history if e.get('status') == 'success'])
      failed = len([e for e in self.history if e.get('status') == 'failed'])
      pending = len([e for e in self.history if e.get('status') == 'pending'])
      
      # API usage stats
      api_usage = {}
      for entry in self.history:
        api = entry.get('api_used', 'unknown')
        if api not in api_usage:
          api_usage[api] = {'total': 0, 'successful': 0, 'failed': 0}
        api_usage[api]['total'] += 1
        if entry.get('status') == 'success':
          api_usage[api]['successful'] += 1
        elif entry.get('status') == 'failed':
          api_usage[api]['failed'] += 1
      
      # Calculate success rate
      success_rate = (successful / total_attempts * 100) if total_attempts > 0 else 0
      
      return {
        'total_attempts': total_attempts,
        'successful': successful,
        'failed': failed,
        'pending': pending,
        'success_rate_percent': round(success_rate, 1),
        'api_usage': api_usage,
        'oldest_entry': self.history[-1]['timestamp'] if self.history else None,
        'newest_entry': self.history[0]['timestamp'] if self.history else None
      }
      
    except Exception as e:
      debug_log(f"Error calculating transcription statistics: {e}", "ERROR")
      return {} 