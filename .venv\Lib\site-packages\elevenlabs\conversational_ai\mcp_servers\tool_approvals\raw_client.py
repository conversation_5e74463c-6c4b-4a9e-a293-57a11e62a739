# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ....core.api_error import ApiError
from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.http_response import AsyncHttpResponse, HttpResponse
from ....core.jsonable_encoder import jsonable_encoder
from ....core.request_options import RequestOptions
from ....core.unchecked_base_model import construct_type
from ....errors.unprocessable_entity_error import UnprocessableEntityError
from ....types.http_validation_error import HttpValidationError
from ....types.mcp_server_response_model import McpServerResponseModel
from ....types.mcp_tool_approval_policy import McpToolApprovalPolicy

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawToolApprovalsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self,
        mcp_server_id: str,
        *,
        tool_name: str,
        tool_description: str,
        input_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        approval_policy: typing.Optional[McpToolApprovalPolicy] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[McpServerResponseModel]:
        """
        Add approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            The name of the MCP tool

        tool_description : str
            The description of the MCP tool

        input_schema : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The input schema of the MCP tool (the schema defined on the MCP server before ElevenLabs does any extra processing)

        approval_policy : typing.Optional[McpToolApprovalPolicy]
            The tool-level approval policy

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[McpServerResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/mcp-servers/{jsonable_encoder(mcp_server_id)}/tool-approvals",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "tool_name": tool_name,
                "tool_description": tool_description,
                "input_schema": input_schema,
                "approval_policy": approval_policy,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    McpServerResponseModel,
                    construct_type(
                        type_=McpServerResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def delete(
        self, mcp_server_id: str, tool_name: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[McpServerResponseModel]:
        """
        Remove approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            Name of the MCP tool to remove approval for.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[McpServerResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/mcp-servers/{jsonable_encoder(mcp_server_id)}/tool-approvals/{jsonable_encoder(tool_name)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    McpServerResponseModel,
                    construct_type(
                        type_=McpServerResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawToolApprovalsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self,
        mcp_server_id: str,
        *,
        tool_name: str,
        tool_description: str,
        input_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = OMIT,
        approval_policy: typing.Optional[McpToolApprovalPolicy] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[McpServerResponseModel]:
        """
        Add approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            The name of the MCP tool

        tool_description : str
            The description of the MCP tool

        input_schema : typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]]
            The input schema of the MCP tool (the schema defined on the MCP server before ElevenLabs does any extra processing)

        approval_policy : typing.Optional[McpToolApprovalPolicy]
            The tool-level approval policy

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[McpServerResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/mcp-servers/{jsonable_encoder(mcp_server_id)}/tool-approvals",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "tool_name": tool_name,
                "tool_description": tool_description,
                "input_schema": input_schema,
                "approval_policy": approval_policy,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    McpServerResponseModel,
                    construct_type(
                        type_=McpServerResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def delete(
        self, mcp_server_id: str, tool_name: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[McpServerResponseModel]:
        """
        Remove approval for a specific MCP tool when using per-tool approval mode.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        tool_name : str
            Name of the MCP tool to remove approval for.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[McpServerResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/mcp-servers/{jsonable_encoder(mcp_server_id)}/tool-approvals/{jsonable_encoder(tool_name)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    McpServerResponseModel,
                    construct_type(
                        type_=McpServerResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
