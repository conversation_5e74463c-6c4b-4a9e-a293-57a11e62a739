#!/usr/bin/env python3
"""
Whisp2 - Voice Transcription Application
Main entry point for the application.
"""

import sys
import os

# Suppress pygame welcome messages and warnings before any pygame imports
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'

# Suppress pkg_resources warnings that pygame might trigger
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="pygame")

import traceback
import tkinter as tk
from tkinter import messagebox
from src.core.app import WhispApp


def check_single_instance():
    """Ensure only one instance of the application is running."""
    try:
        # Simple approach: try to bind to a specific port
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            # Try to bind to a specific port that only one instance can use
            sock.bind(('127.0.0.1', 19847))  # Random high port number
            return sock  # Return the socket to keep it bound
        except socket.error:
            # Port is already in use, another instance is running
            sock.close()
            return None

    except Exception as e:
        print(f"Warning: Could not check for single instance: {e}")
        return True  # Allow startup if check fails


def main():
  """Main entry point with error handling."""
  # Check for single instance
  lock_file = check_single_instance()
  if lock_file is None:
    # Another instance is already running
    try:
      root = tk.Tk()
      root.withdraw()  # Hide the main window
      messagebox.showwarning("Whisp2", "Another instance of Whisp2 is already running.")
      root.destroy()
    except:
      print("Another instance of Whisp2 is already running.")
    sys.exit(0)

  try:
    app = WhispApp()
    app.run()
  except Exception as e:
    # Log the error and show a user-friendly message
    error_msg = f"Failed to start Whisp2: {str(e)}\n\n{traceback.format_exc()}"
    print(error_msg)

    # Try to show a GUI error dialog if possible
    try:
      root = tk.Tk()
      root.withdraw()  # Hide the main window
      messagebox.showerror("Whisp2 Error", f"Failed to start application:\n{str(e)}")
      root.destroy()
    except:
      pass  # If GUI fails, just print to console

    sys.exit(1)
  finally:
    # Clean up socket
    if lock_file and lock_file is not True and hasattr(lock_file, 'close'):
      try:
        lock_file.close()
      except:
        pass


if __name__ == "__main__":
  main()