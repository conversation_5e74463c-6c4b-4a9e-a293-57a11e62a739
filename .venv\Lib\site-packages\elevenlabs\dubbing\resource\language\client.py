# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.language_added_response import LanguageAddedResponse
from .raw_client import AsyncRaw<PERSON>anguageClient, RawLanguageClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class LanguageClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawLanguageClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawLanguageClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawLanguageClient
        """
        return self._raw_client

    def add(
        self,
        dubbing_id: str,
        *,
        language: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> LanguageAddedResponse:
        """
        Adds the given ElevenLab Turbo V2/V2.5 language code to the resource. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language : typing.Optional[str]
            The Target language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        LanguageAddedResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.language.add(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._raw_client.add(dubbing_id, language=language, request_options=request_options)
        return _response.data


class AsyncLanguageClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawLanguageClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawLanguageClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawLanguageClient
        """
        return self._raw_client

    async def add(
        self,
        dubbing_id: str,
        *,
        language: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> LanguageAddedResponse:
        """
        Adds the given ElevenLab Turbo V2/V2.5 language code to the resource. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language : typing.Optional[str]
            The Target language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        LanguageAddedResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.language.add(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.add(dubbing_id, language=language, request_options=request_options)
        return _response.data
