# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .chapter_content_block_input_model_sub_type import ChapterContentBlockInputModelSubType
from .chapter_content_paragraph_tts_node_input_model import ChapterContentParagraphTtsNodeInputModel


class ChapterContentBlockInputModel(UncheckedBaseModel):
    sub_type: typing.Optional[ChapterContentBlockInputModelSubType] = None
    nodes: typing.List[ChapterContentParagraphTtsNodeInputModel]
    block_id: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
