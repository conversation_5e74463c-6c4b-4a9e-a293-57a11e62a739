# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.similar_voices_for_speaker_response import SimilarVoicesForSpeakerResponse
from ....types.speaker_updated_response import SpeakerUpdatedResponse
from .raw_client import AsyncRawSpeakerClient, RawSpeakerClient
from .segment.client import AsyncSegmentClient, SegmentClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SpeakerClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSpeakerClient(client_wrapper=client_wrapper)
        self.segment = SegmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSpeakerClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSpeakerClient
        """
        return self._raw_client

    def update(
        self,
        dubbing_id: str,
        speaker_id: str,
        *,
        voice_id: typing.Optional[str] = OMIT,
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SpeakerUpdatedResponse:
        """
        Amend the metadata associated with a speaker, such as their voice. Both voice cloning and using voices from the ElevenLabs library are supported.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        voice_id : typing.Optional[str]
            Either the identifier of a voice from the ElevenLabs voice library, or one of ['track-clone', 'clip-clone'].

        languages : typing.Optional[typing.Sequence[str]]
            Languages to apply these changes to. If empty, will apply to all languages.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SpeakerUpdatedResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.speaker.update(
            dubbing_id="dubbing_id",
            speaker_id="speaker_id",
        )
        """
        _response = self._raw_client.update(
            dubbing_id, speaker_id, voice_id=voice_id, languages=languages, request_options=request_options
        )
        return _response.data

    def find_similar_voices(
        self, dubbing_id: str, speaker_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SimilarVoicesForSpeakerResponse:
        """
        Fetch the top 10 similar voices to a speaker, including the voice IDs, names, descriptions, and, where possible, a sample audio recording.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SimilarVoicesForSpeakerResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.speaker.find_similar_voices(
            dubbing_id="dubbing_id",
            speaker_id="speaker_id",
        )
        """
        _response = self._raw_client.find_similar_voices(dubbing_id, speaker_id, request_options=request_options)
        return _response.data


class AsyncSpeakerClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSpeakerClient(client_wrapper=client_wrapper)
        self.segment = AsyncSegmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSpeakerClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSpeakerClient
        """
        return self._raw_client

    async def update(
        self,
        dubbing_id: str,
        speaker_id: str,
        *,
        voice_id: typing.Optional[str] = OMIT,
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SpeakerUpdatedResponse:
        """
        Amend the metadata associated with a speaker, such as their voice. Both voice cloning and using voices from the ElevenLabs library are supported.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        voice_id : typing.Optional[str]
            Either the identifier of a voice from the ElevenLabs voice library, or one of ['track-clone', 'clip-clone'].

        languages : typing.Optional[typing.Sequence[str]]
            Languages to apply these changes to. If empty, will apply to all languages.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SpeakerUpdatedResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.speaker.update(
                dubbing_id="dubbing_id",
                speaker_id="speaker_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            dubbing_id, speaker_id, voice_id=voice_id, languages=languages, request_options=request_options
        )
        return _response.data

    async def find_similar_voices(
        self, dubbing_id: str, speaker_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SimilarVoicesForSpeakerResponse:
        """
        Fetch the top 10 similar voices to a speaker, including the voice IDs, names, descriptions, and, where possible, a sample audio recording.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SimilarVoicesForSpeakerResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.speaker.find_similar_voices(
                dubbing_id="dubbing_id",
                speaker_id="speaker_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.find_similar_voices(dubbing_id, speaker_id, request_options=request_options)
        return _response.data
