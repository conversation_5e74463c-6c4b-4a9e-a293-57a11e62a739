# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.resource_metadata_response_model import ResourceMetadataResponseModel
from ...types.workspace_resource_type import WorkspaceResourceType
from .raw_client import AsyncRawResourcesClient, RawResourcesClient
from .types.body_share_workspace_resource_v_1_workspace_resources_resource_id_share_post_role import (
    BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
)

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ResourcesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawResourcesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawResourcesClient
        """
        return self._raw_client

    def get(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ResourceMetadataResponseModel:
        """
        Gets the metadata of a resource by ID.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ResourceMetadataResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.resources.get(
            resource_id="resource_id",
            resource_type="voice",
        )
        """
        _response = self._raw_client.get(resource_id, resource_type=resource_type, request_options=request_options)
        return _response.data

    def share(
        self,
        resource_id: str,
        *,
        role: BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Grants a role on a workspace resource to a user or a group. It overrides any existing role this user/service account/group/workspace api key has on the resource. To target a user or service account, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. The resource will be shared with the service account associated with the api key. You must have admin access to the resource to share it.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        role : BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole
            Role to update the target principal with.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user or service account.

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.resources.share(
            resource_id="resource_id",
            role="admin",
            resource_type="voice",
        )
        """
        _response = self._raw_client.share(
            resource_id,
            role=role,
            resource_type=resource_type,
            user_email=user_email,
            group_id=group_id,
            workspace_api_key_id=workspace_api_key_id,
            request_options=request_options,
        )
        return _response.data

    def unshare(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Removes any existing role on a workspace resource from a user, service account, group or workspace api key. To target a user or service account, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. The resource will be unshared from the service account associated with the api key. You must have admin access to the resource to unshare it. You cannot remove permissions from the user who created the resource.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user or service account.

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.resources.unshare(
            resource_id="resource_id",
            resource_type="voice",
        )
        """
        _response = self._raw_client.unshare(
            resource_id,
            resource_type=resource_type,
            user_email=user_email,
            group_id=group_id,
            workspace_api_key_id=workspace_api_key_id,
            request_options=request_options,
        )
        return _response.data


class AsyncResourcesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawResourcesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawResourcesClient
        """
        return self._raw_client

    async def get(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ResourceMetadataResponseModel:
        """
        Gets the metadata of a resource by ID.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ResourceMetadataResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.resources.get(
                resource_id="resource_id",
                resource_type="voice",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(
            resource_id, resource_type=resource_type, request_options=request_options
        )
        return _response.data

    async def share(
        self,
        resource_id: str,
        *,
        role: BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Grants a role on a workspace resource to a user or a group. It overrides any existing role this user/service account/group/workspace api key has on the resource. To target a user or service account, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. The resource will be shared with the service account associated with the api key. You must have admin access to the resource to share it.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        role : BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePostRole
            Role to update the target principal with.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user or service account.

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.resources.share(
                resource_id="resource_id",
                role="admin",
                resource_type="voice",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.share(
            resource_id,
            role=role,
            resource_type=resource_type,
            user_email=user_email,
            group_id=group_id,
            workspace_api_key_id=workspace_api_key_id,
            request_options=request_options,
        )
        return _response.data

    async def unshare(
        self,
        resource_id: str,
        *,
        resource_type: WorkspaceResourceType,
        user_email: typing.Optional[str] = OMIT,
        group_id: typing.Optional[str] = OMIT,
        workspace_api_key_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Removes any existing role on a workspace resource from a user, service account, group or workspace api key. To target a user or service account, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. The resource will be unshared from the service account associated with the api key. You must have admin access to the resource to unshare it. You cannot remove permissions from the user who created the resource.

        Parameters
        ----------
        resource_id : str
            The ID of the target resource.

        resource_type : WorkspaceResourceType
            Resource type of the target resource.

        user_email : typing.Optional[str]
            The email of the user or service account.

        group_id : typing.Optional[str]
            The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'.

        workspace_api_key_id : typing.Optional[str]
            The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.resources.unshare(
                resource_id="resource_id",
                resource_type="voice",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.unshare(
            resource_id,
            resource_type=resource_type,
            user_email=user_email,
            group_id=group_id,
            workspace_api_key_id=workspace_api_key_id,
            request_options=request_options,
        )
        return _response.data
