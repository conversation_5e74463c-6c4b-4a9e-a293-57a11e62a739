# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, Sync<PERSON><PERSON>Wrapper
from ...core.request_options import RequestOptions
from ...types.mcp_server_config_input import McpServerConfigInput
from ...types.mcp_server_response_model import McpServerResponseModel
from ...types.mcp_servers_response_model import McpServersResponseModel
from .approval_policy.client import ApprovalPolicyClient, AsyncApprovalPolicyClient
from .raw_client import AsyncRawMcpServersClient, RawMcpServersClient
from .tool_approvals.client import AsyncToolApprovalsClient, ToolApprovalsClient
from .tools.client import AsyncToolsClient, ToolsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class McpServersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawMcpServersClient(client_wrapper=client_wrapper)
        self.tools = ToolsClient(client_wrapper=client_wrapper)

        self.approval_policy = ApprovalPolicyClient(client_wrapper=client_wrapper)

        self.tool_approvals = ToolApprovalsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawMcpServersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawMcpServersClient
        """
        return self._raw_client

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> McpServersResponseModel:
        """
        Retrieve all MCP server configurations available in the workspace.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServersResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self, *, config: McpServerConfigInput, request_options: typing.Optional[RequestOptions] = None
    ) -> McpServerResponseModel:
        """
        Create a new MCP server configuration in the workspace.

        Parameters
        ----------
        config : McpServerConfigInput
            Configuration details for the MCP Server.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs, McpServerConfigInput

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.create(
            config=McpServerConfigInput(
                url="url",
                name="name",
            ),
        )
        """
        _response = self._raw_client.create(config=config, request_options=request_options)
        return _response.data

    def get(
        self, mcp_server_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> McpServerResponseModel:
        """
        Retrieve a specific MCP server configuration from the workspace.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.get(
            mcp_server_id="mcp_server_id",
        )
        """
        _response = self._raw_client.get(mcp_server_id, request_options=request_options)
        return _response.data


class AsyncMcpServersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawMcpServersClient(client_wrapper=client_wrapper)
        self.tools = AsyncToolsClient(client_wrapper=client_wrapper)

        self.approval_policy = AsyncApprovalPolicyClient(client_wrapper=client_wrapper)

        self.tool_approvals = AsyncToolApprovalsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawMcpServersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawMcpServersClient
        """
        return self._raw_client

    async def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> McpServersResponseModel:
        """
        Retrieve all MCP server configurations available in the workspace.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServersResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self, *, config: McpServerConfigInput, request_options: typing.Optional[RequestOptions] = None
    ) -> McpServerResponseModel:
        """
        Create a new MCP server configuration in the workspace.

        Parameters
        ----------
        config : McpServerConfigInput
            Configuration details for the MCP Server.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs, McpServerConfigInput

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.create(
                config=McpServerConfigInput(
                    url="url",
                    name="name",
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(config=config, request_options=request_options)
        return _response.data

    async def get(
        self, mcp_server_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> McpServerResponseModel:
        """
        Retrieve a specific MCP server configuration from the workspace.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.get(
                mcp_server_id="mcp_server_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(mcp_server_id, request_options=request_options)
        return _response.data
