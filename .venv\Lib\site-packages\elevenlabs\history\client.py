# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.delete_history_item_response import DeleteHistory<PERSON>temResponse
from ..types.get_speech_history_response import GetSpeechHistoryResponse
from ..types.speech_history_item_response import SpeechHistoryItemResponse
from .raw_client import AsyncRawH<PERSON>oryClient, RawHistoryClient
from .types.history_list_request_source import HistoryListRequestSource

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class HistoryClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawHistoryClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawHistoryClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawHistoryClient
        """
        return self._raw_client

    def list(
        self,
        *,
        page_size: typing.Optional[int] = None,
        start_after_history_item_id: typing.Optional[str] = None,
        voice_id: typing.Optional[str] = None,
        search: typing.Optional[str] = None,
        source: typing.Optional[HistoryListRequestSource] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetSpeechHistoryResponse:
        """
        Returns a list of your generated audio.

        Parameters
        ----------
        page_size : typing.Optional[int]
            How many history items to return at maximum. Can not exceed 1000, defaults to 100.

        start_after_history_item_id : typing.Optional[str]
            After which ID to start fetching, use this parameter to paginate across a large collection of history items. In case this parameter is not provided history items will be fetched starting from the most recently created one ordered descending by their creation date.

        voice_id : typing.Optional[str]
            ID of the voice to be filtered for. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        search : typing.Optional[str]
            Search term used for filtering history items. If provided, source becomes required.

        source : typing.Optional[HistoryListRequestSource]
            Source of the generated history item

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetSpeechHistoryResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.history.list()
        """
        _response = self._raw_client.list(
            page_size=page_size,
            start_after_history_item_id=start_after_history_item_id,
            voice_id=voice_id,
            search=search,
            source=source,
            request_options=request_options,
        )
        return _response.data

    def get(
        self, history_item_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SpeechHistoryItemResponse:
        """
        Retrieves a history item.

        Parameters
        ----------
        history_item_id : str
            ID of the history item to be used. You can use the [Get generated items](/docs/api-reference/history/get-all) endpoint to retrieve a list of history items.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SpeechHistoryItemResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.history.get(
            history_item_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.get(history_item_id, request_options=request_options)
        return _response.data

    def delete(
        self, history_item_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteHistoryItemResponse:
        """
        Delete a history item by its ID

        Parameters
        ----------
        history_item_id : str
            ID of the history item to be used. You can use the [Get generated items](/docs/api-reference/history/get-all) endpoint to retrieve a list of history items.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteHistoryItemResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.history.delete(
            history_item_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.delete(history_item_id, request_options=request_options)
        return _response.data

    def get_audio(
        self, history_item_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Iterator[bytes]:
        """
        Returns the audio of an history item.

        Parameters
        ----------
        history_item_id : str
            ID of the history item to be used. You can use the [Get generated items](/docs/api-reference/history/get-all) endpoint to retrieve a list of history items.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            The audio file of the history item.
        """
        with self._raw_client.get_audio(history_item_id, request_options=request_options) as r:
            yield from r.data

    def download(
        self,
        *,
        history_item_ids: typing.Sequence[str],
        output_format: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Download one or more history items. If one history item ID is provided, we will return a single audio file. If more than one history item IDs are provided, we will provide the history items packed into a .zip file.

        Parameters
        ----------
        history_item_ids : typing.Sequence[str]
            A list of history items to download, you can get IDs of history items and other metadata using the GET https://api.elevenlabs.io/v1/history endpoint.

        output_format : typing.Optional[str]
            Output format to transcode the audio file, can be wav or default.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            The requested audio file, or a zip file containing multiple audio files when multiple history items are requested.
        """
        with self._raw_client.download(
            history_item_ids=history_item_ids, output_format=output_format, request_options=request_options
        ) as r:
            yield from r.data


class AsyncHistoryClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawHistoryClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawHistoryClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawHistoryClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        page_size: typing.Optional[int] = None,
        start_after_history_item_id: typing.Optional[str] = None,
        voice_id: typing.Optional[str] = None,
        search: typing.Optional[str] = None,
        source: typing.Optional[HistoryListRequestSource] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetSpeechHistoryResponse:
        """
        Returns a list of your generated audio.

        Parameters
        ----------
        page_size : typing.Optional[int]
            How many history items to return at maximum. Can not exceed 1000, defaults to 100.

        start_after_history_item_id : typing.Optional[str]
            After which ID to start fetching, use this parameter to paginate across a large collection of history items. In case this parameter is not provided history items will be fetched starting from the most recently created one ordered descending by their creation date.

        voice_id : typing.Optional[str]
            ID of the voice to be filtered for. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        search : typing.Optional[str]
            Search term used for filtering history items. If provided, source becomes required.

        source : typing.Optional[HistoryListRequestSource]
            Source of the generated history item

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetSpeechHistoryResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.history.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            page_size=page_size,
            start_after_history_item_id=start_after_history_item_id,
            voice_id=voice_id,
            search=search,
            source=source,
            request_options=request_options,
        )
        return _response.data

    async def get(
        self, history_item_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SpeechHistoryItemResponse:
        """
        Retrieves a history item.

        Parameters
        ----------
        history_item_id : str
            ID of the history item to be used. You can use the [Get generated items](/docs/api-reference/history/get-all) endpoint to retrieve a list of history items.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SpeechHistoryItemResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.history.get(
                history_item_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(history_item_id, request_options=request_options)
        return _response.data

    async def delete(
        self, history_item_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteHistoryItemResponse:
        """
        Delete a history item by its ID

        Parameters
        ----------
        history_item_id : str
            ID of the history item to be used. You can use the [Get generated items](/docs/api-reference/history/get-all) endpoint to retrieve a list of history items.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteHistoryItemResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.history.delete(
                history_item_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(history_item_id, request_options=request_options)
        return _response.data

    async def get_audio(
        self, history_item_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.AsyncIterator[bytes]:
        """
        Returns the audio of an history item.

        Parameters
        ----------
        history_item_id : str
            ID of the history item to be used. You can use the [Get generated items](/docs/api-reference/history/get-all) endpoint to retrieve a list of history items.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            The audio file of the history item.
        """
        async with self._raw_client.get_audio(history_item_id, request_options=request_options) as r:
            async for _chunk in r.data:
                yield _chunk

    async def download(
        self,
        *,
        history_item_ids: typing.Sequence[str],
        output_format: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Download one or more history items. If one history item ID is provided, we will return a single audio file. If more than one history item IDs are provided, we will provide the history items packed into a .zip file.

        Parameters
        ----------
        history_item_ids : typing.Sequence[str]
            A list of history items to download, you can get IDs of history items and other metadata using the GET https://api.elevenlabs.io/v1/history endpoint.

        output_format : typing.Optional[str]
            Output format to transcode the audio file, can be wav or default.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            The requested audio file, or a zip file containing multiple audio files when multiple history items are requested.
        """
        async with self._raw_client.download(
            history_item_ids=history_item_ids, output_format=output_format, request_options=request_options
        ) as r:
            async for _chunk in r.data:
                yield _chunk
