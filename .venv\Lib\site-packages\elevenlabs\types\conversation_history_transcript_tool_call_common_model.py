# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_history_transcript_tool_call_common_model_tool_details import (
    ConversationHistoryTranscriptToolCallCommonModelToolDetails,
)


class ConversationHistoryTranscriptToolCallCommonModel(UncheckedBaseModel):
    type: typing.Optional[str] = None
    request_id: str
    tool_name: str
    params_as_json: str
    tool_has_been_called: bool
    tool_details: typing.Optional[ConversationHistoryTranscriptToolCallCommonModelToolDetails] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
