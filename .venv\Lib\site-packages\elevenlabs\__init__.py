# This file was auto-generated by <PERSON><PERSON> from our API Definition.

# isort: skip_file

from .types import (
    AddChapterResponseModel,
    AddKnowledgeBaseResponseModel,
    AddProjectRequest,
    AddProjectResponseModel,
    AddPronunciationDictionaryResponseModel,
    AddPronunciationDictionaryResponseModelPermissionOnResource,
    AddSharingVoiceRequest,
    AddVoiceIvcResponseModel,
    AddVoiceResponseModel,
    AddWorkspaceGroupMemberResponseModel,
    AddWorkspaceInviteResponseModel,
    AdditionalFormatResponseModel,
    AdditionalFormats,
    Age,
    AgentBan,
    AgentCallLimits,
    AgentConfig,
    AgentConfigOverride,
    AgentConfigOverrideConfig,
    AgentMetadataResponseModel,
    AgentPlatformSettingsRequestModel,
    AgentPlatformSettingsResponseModel,
    AgentSimulatedChatTestResponseModel,
    AgentSummaryResponseModel,
    AgentTransfer,
    AgentWorkspaceOverridesInput,
    AgentWorkspaceOverridesOutput,
    Alignment,
    AllowlistItem,
    ArrayJsonSchemaPropertyInput,
    ArrayJsonSchemaPropertyInputItems,
    ArrayJsonSchemaPropertyOutput,
    ArrayJsonSchemaPropertyOutputItems,
    AsrConversationalConfig,
    AsrInputFormat,
    AsrProvider,
    AsrQuality,
    AudioNativeCreateProjectResponseModel,
    AudioNativeEditContentResponseModel,
    AudioNativeProjectSettingsResponseModel,
    AudioNativeProjectSettingsResponseModelStatus,
    AudioOutput,
    AudioOutputMulti,
    AudioWithTimestampsResponse,
    AuthSettings,
    AuthorizationMethod,
    BanReasonType,
    BatchCallDetailedResponse,
    BatchCallRecipientStatus,
    BatchCallResponse,
    BatchCallStatus,
    BodyAddChapterToAProjectV1ProjectsProjectIdChaptersAddPost,
    BodyAddProjectV1ProjectsAddPostApplyTextNormalization,
    BodyAddProjectV1ProjectsAddPostFiction,
    BodyAddProjectV1ProjectsAddPostSourceType,
    BodyAddProjectV1ProjectsAddPostTargetAudience,
    BodyAddToKnowledgeBaseV1ConvaiAddToKnowledgeBasePost,
    BodyAddToKnowledgeBaseV1ConvaiAgentsAgentIdAddToKnowledgeBasePost,
    BodyCreatePodcastV1ProjectsPodcastCreatePost,
    BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale,
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode,
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Bulletin,
    BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation,
    BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Text,
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Url,
    BodyEditBasicProjectInfoV1ProjectsProjectIdPost,
    BodyEditChapterV1ProjectsProjectIdChaptersChapterIdPatch,
    BodyEditProjectContentV1ProjectsProjectIdContentPost,
    BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge,
    BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender,
    BodyRetrieveVoiceSampleAudioV1VoicesPvcVoiceIdSamplesSampleIdAudioGet,
    BodyStreamChapterAudioV1ProjectsProjectIdChaptersChapterIdSnapshotsChapterSnapshotIdStreamPost,
    BodyStreamProjectAudioV1ProjectsProjectIdSnapshotsProjectSnapshotIdStreamPost,
    BreakdownTypes,
    BuiltInTools,
    ChapterContentBlockExtendableNodeResponseModel,
    ChapterContentBlockInputModel,
    ChapterContentBlockInputModelSubType,
    ChapterContentBlockResponseModel,
    ChapterContentBlockResponseModelNodesItem,
    ChapterContentBlockResponseModelNodesItem_Other,
    ChapterContentBlockResponseModelNodesItem_TtsNode,
    ChapterContentBlockTtsNodeResponseModel,
    ChapterContentInputModel,
    ChapterContentParagraphTtsNodeInputModel,
    ChapterContentResponseModel,
    ChapterResponse,
    ChapterSnapshotExtendedResponseModel,
    ChapterSnapshotResponse,
    ChapterSnapshotsResponse,
    ChapterState,
    ChapterStatisticsResponse,
    ChapterWithContentResponseModel,
    ChapterWithContentResponseModelState,
    CharacterAlignmentModel,
    CharacterAlignmentResponseModel,
    CharacterUsageResponse,
    ClientEvent,
    ClientToolConfigInput,
    ClientToolConfigOutput,
    CloseConnection,
    CloseContext,
    CloseSocket,
    ConvAiDynamicVariable,
    ConvAiSecretLocator,
    ConvAiStoredSecretDependencies,
    ConvAiStoredSecretDependenciesAgentToolsItem,
    ConvAiStoredSecretDependenciesAgentToolsItem_Available,
    ConvAiStoredSecretDependenciesAgentToolsItem_Unknown,
    ConvAiStoredSecretDependenciesToolsItem,
    ConvAiStoredSecretDependenciesToolsItem_Available,
    ConvAiStoredSecretDependenciesToolsItem_Unknown,
    ConvAiUserSecretDbModel,
    ConvAiWebhooks,
    ConvAiWorkspaceStoredSecretConfig,
    ConversationChargingCommonModel,
    ConversationConfig,
    ConversationConfigClientOverrideConfigInput,
    ConversationConfigClientOverrideConfigOutput,
    ConversationConfigClientOverrideInput,
    ConversationConfigClientOverrideOutput,
    ConversationConfigOverride,
    ConversationConfigOverrideConfig,
    ConversationDeletionSettings,
    ConversationHistoryAnalysisCommonModel,
    ConversationHistoryBatchCallModel,
    ConversationHistoryErrorCommonModel,
    ConversationHistoryEvaluationCriteriaResultCommonModel,
    ConversationHistoryFeedbackCommonModel,
    ConversationHistoryMetadataCommonModel,
    ConversationHistoryMetadataCommonModelPhoneCall,
    ConversationHistoryMetadataCommonModelPhoneCall_SipTrunking,
    ConversationHistoryMetadataCommonModelPhoneCall_Twilio,
    ConversationHistoryRagUsageCommonModel,
    ConversationHistorySipTrunkingPhoneCallModel,
    ConversationHistorySipTrunkingPhoneCallModelDirection,
    ConversationHistoryTranscriptCommonModelInput,
    ConversationHistoryTranscriptCommonModelInputRole,
    ConversationHistoryTranscriptCommonModelInputSourceMedium,
    ConversationHistoryTranscriptCommonModelOutput,
    ConversationHistoryTranscriptCommonModelOutputRole,
    ConversationHistoryTranscriptCommonModelOutputSourceMedium,
    ConversationHistoryTranscriptToolCallClientDetails,
    ConversationHistoryTranscriptToolCallCommonModel,
    ConversationHistoryTranscriptToolCallCommonModelToolDetails,
    ConversationHistoryTranscriptToolCallCommonModelToolDetails_Client,
    ConversationHistoryTranscriptToolCallCommonModelToolDetails_Webhook,
    ConversationHistoryTranscriptToolCallWebhookDetails,
    ConversationHistoryTranscriptToolResultCommonModel,
    ConversationHistoryTwilioPhoneCallModel,
    ConversationHistoryTwilioPhoneCallModelDirection,
    ConversationInitiationClientDataConfigInput,
    ConversationInitiationClientDataConfigOutput,
    ConversationInitiationClientDataInternal,
    ConversationInitiationClientDataInternalDynamicVariablesValue,
    ConversationInitiationClientDataRequestInput,
    ConversationInitiationClientDataRequestInputDynamicVariablesValue,
    ConversationInitiationClientDataRequestOutput,
    ConversationInitiationClientDataRequestOutputDynamicVariablesValue,
    ConversationInitiationClientDataWebhook,
    ConversationInitiationClientDataWebhookRequestHeadersValue,
    ConversationSignedUrlResponseModel,
    ConversationSimulationSpecification,
    ConversationSimulationSpecificationDynamicVariablesValue,
    ConversationSummaryResponseModel,
    ConversationSummaryResponseModelStatus,
    ConversationTokenDbModel,
    ConversationTokenPurpose,
    ConversationTurnMetrics,
    ConversationalConfig,
    ConvertChapterResponseModel,
    ConvertProjectResponseModel,
    CreateAgentResponseModel,
    CreateAudioNativeProjectRequest,
    CreatePhoneNumberResponseModel,
    CreatePreviouslyGeneratedVoiceRequest,
    CreatePronunciationDictionaryResponseModel,
    CreateSipTrunkPhoneNumberRequest,
    CreateTranscriptRequest,
    CreateTwilioPhoneNumberRequest,
    CustomLlm,
    CustomLlmRequestHeadersValue,
    DashboardCallSuccessChartModel,
    DashboardCriteriaChartModel,
    DashboardDataCollectionChartModel,
    DataCollectionResultCommonModel,
    DeleteChapterRequest,
    DeleteChapterResponseModel,
    DeleteDubbingResponseModel,
    DeleteHistoryItemResponse,
    DeleteProjectRequest,
    DeleteProjectResponseModel,
    DeleteSampleResponse,
    DeleteVoiceResponseModel,
    DeleteVoiceSampleResponseModel,
    DeleteWorkspaceGroupMemberResponseModel,
    DeleteWorkspaceInviteResponseModel,
    DeleteWorkspaceMemberResponseModel,
    DependentAvailableAgentIdentifier,
    DependentAvailableAgentIdentifierAccessLevel,
    DependentAvailableAgentToolIdentifier,
    DependentAvailableAgentToolIdentifierAccessLevel,
    DependentAvailableToolIdentifier,
    DependentAvailableToolIdentifierAccessLevel,
    DependentPhoneNumberIdentifier,
    DependentUnknownAgentIdentifier,
    DependentUnknownAgentToolIdentifier,
    DependentUnknownToolIdentifier,
    DialogueInput,
    DialogueInputResponseModel,
    DoDubbingResponse,
    DocumentUsageModeEnum,
    DocxExportOptions,
    DubbedSegment,
    DubbingMediaMetadata,
    DubbingMediaReference,
    DubbingMetadataResponse,
    DubbingRenderResponseModel,
    DubbingResource,
    DynamicVariablesConfig,
    DynamicVariablesConfigDynamicVariablePlaceholdersValue,
    EditChapterResponseModel,
    EditProjectResponseModel,
    EditVoiceResponseModel,
    EditVoiceSettingsRequest,
    EditVoiceSettingsResponseModel,
    EmbedVariant,
    EmbeddingModelEnum,
    EndCallToolConfig,
    EvaluationSettings,
    EvaluationSuccessResult,
    ExportOptions,
    ExportOptions_Docx,
    ExportOptions_Html,
    ExportOptions_Pdf,
    ExportOptions_SegmentedJson,
    ExportOptions_Srt,
    ExportOptions_Txt,
    ExtendedSubscriptionResponseModelBillingPeriod,
    ExtendedSubscriptionResponseModelCharacterRefreshPeriod,
    ExtendedSubscriptionResponseModelCurrency,
    FeatureStatusCommonModel,
    FeaturesUsageCommonModel,
    FeedbackItem,
    FinalOutput,
    FinalOutputMulti,
    FineTuningResponse,
    FineTuningResponseModelStateValue,
    FlushContext,
    ForcedAlignmentCharacterResponseModel,
    ForcedAlignmentResponseModel,
    ForcedAlignmentWordResponseModel,
    Gender,
    GenerateVoiceRequest,
    GenerationConfig,
    GetAgentEmbedResponseModel,
    GetAgentKnowledgebaseSizeResponseModel,
    GetAgentLinkResponseModel,
    GetAgentResponseModel,
    GetAgentResponseModelPhoneNumbersItem,
    GetAgentResponseModelPhoneNumbersItem_SipTrunk,
    GetAgentResponseModelPhoneNumbersItem_Twilio,
    GetAgentsPageResponseModel,
    GetAudioNativeProjectSettingsResponseModel,
    GetChapterRequest,
    GetChapterSnapshotsRequest,
    GetChaptersRequest,
    GetChaptersResponse,
    GetConvAiDashboardSettingsResponseModel,
    GetConvAiDashboardSettingsResponseModelChartsItem,
    GetConvAiDashboardSettingsResponseModelChartsItem_CallSuccess,
    GetConvAiDashboardSettingsResponseModelChartsItem_Criteria,
    GetConvAiDashboardSettingsResponseModelChartsItem_DataCollection,
    GetConvAiSettingsResponseModel,
    GetConversationResponseModel,
    GetConversationResponseModelStatus,
    GetConversationsPageResponseModel,
    GetKnowledgeBaseDependentAgentsResponseModel,
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem,
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Available,
    GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Unknown,
    GetKnowledgeBaseFileResponseModel,
    GetKnowledgeBaseListResponseModel,
    GetKnowledgeBaseListResponseModelDocumentsItem,
    GetKnowledgeBaseListResponseModelDocumentsItem_File,
    GetKnowledgeBaseListResponseModelDocumentsItem_Text,
    GetKnowledgeBaseListResponseModelDocumentsItem_Url,
    GetKnowledgeBaseSummaryFileResponseModel,
    GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem,
    GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Available,
    GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Unknown,
    GetKnowledgeBaseSummaryTextResponseModel,
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem,
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Available,
    GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Unknown,
    GetKnowledgeBaseSummaryUrlResponseModel,
    GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem,
    GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Available,
    GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Unknown,
    GetKnowledgeBaseTextResponseModel,
    GetKnowledgeBaseUrlResponseModel,
    GetLibraryVoicesResponse,
    GetPhoneNumberResponse,
    GetPhoneNumberSipTrunkResponseModel,
    GetPhoneNumberTwilioResponseModel,
    GetProjectRequest,
    GetProjectsRequest,
    GetProjectsResponse,
    GetPronunciationDictionariesMetadataResponseModel,
    GetPronunciationDictionariesResponse,
    GetPronunciationDictionaryMetadataResponse,
    GetPronunciationDictionaryMetadataResponseModelPermissionOnResource,
    GetPronunciationDictionaryResponse,
    GetSpeechHistoryResponse,
    GetVoicesResponse,
    GetVoicesV2Response,
    GetWorkspaceSecretsResponseModel,
    HistoryAlignmentResponseModel,
    HistoryAlignmentsResponseModel,
    HistoryItemResponse,
    HtmlExportOptions,
    HttpValidationError,
    ImageAvatar,
    InitialiseContext,
    InitializeConnection,
    InitializeConnectionMulti,
    IntegrationType,
    InvoiceResponse,
    KeepContextAlive,
    KnowledgeBaseDocumentChunkResponseModel,
    KnowledgeBaseDocumentMetadataResponseModel,
    KnowledgeBaseDocumentType,
    KnowledgeBaseLocator,
    LanguageAddedResponse,
    LanguageDetectionToolConfig,
    LanguagePresetInput,
    LanguagePresetOutput,
    LanguagePresetTranslation,
    LanguageResponse,
    LibraryVoiceResponse,
    LibraryVoiceResponseModelCategory,
    ListMcpToolsResponseModel,
    LiteralJsonSchemaProperty,
    LiteralJsonSchemaPropertyConstantValue,
    LiteralJsonSchemaPropertyType,
    Llm,
    LlmCategoryUsage,
    LlmInputOutputTokensUsage,
    LlmTokensCategoryUsage,
    LlmUsageCalculatorLlmResponseModel,
    LlmUsageCalculatorResponseModel,
    LlmUsageInput,
    LlmUsageOutput,
    ManualVerificationFileResponse,
    ManualVerificationResponse,
    McpApprovalPolicy,
    McpServerConfigInput,
    McpServerConfigInputRequestHeadersValue,
    McpServerConfigInputSecretToken,
    McpServerConfigInputUrl,
    McpServerConfigOutput,
    McpServerConfigOutputRequestHeadersValue,
    McpServerConfigOutputSecretToken,
    McpServerConfigOutputUrl,
    McpServerMetadataResponseModel,
    McpServerResponseModel,
    McpServerResponseModelDependentAgentsItem,
    McpServerResponseModelDependentAgentsItem_Available,
    McpServerResponseModelDependentAgentsItem_Unknown,
    McpServerTransport,
    McpServersResponseModel,
    McpToolApprovalHash,
    McpToolApprovalPolicy,
    McpToolConfigInput,
    McpToolConfigOutput,
    MetricRecord,
    MetricType,
    Model,
    ModelRatesResponseModel,
    ModelResponseModelConcurrencyGroup,
    ModelSettingsResponseModel,
    ModerationStatusResponseModel,
    ModerationStatusResponseModelSafetyStatus,
    ModerationStatusResponseModelWarningStatus,
    NormalizedAlignment,
    ObjectJsonSchemaPropertyInput,
    ObjectJsonSchemaPropertyInputPropertiesValue,
    ObjectJsonSchemaPropertyOutput,
    ObjectJsonSchemaPropertyOutputPropertiesValue,
    OrbAvatar,
    OutboundCallRecipient,
    OutboundCallRecipientResponseModel,
    OutputFormat,
    PdfExportOptions,
    PhoneNumberAgentInfo,
    PhoneNumberTransfer,
    PodcastBulletinMode,
    PodcastBulletinModeData,
    PodcastConversationMode,
    PodcastConversationModeData,
    PodcastProjectResponseModel,
    PodcastTextSource,
    PodcastUrlSource,
    PostAgentAvatarResponseModel,
    PostWorkspaceSecretResponseModel,
    PrivacyConfig,
    ProjectCreationMetaResponseModel,
    ProjectCreationMetaResponseModelStatus,
    ProjectCreationMetaResponseModelType,
    ProjectExtendedResponse,
    ProjectExtendedResponseModelAccessLevel,
    ProjectExtendedResponseModelApplyTextNormalization,
    ProjectExtendedResponseModelFiction,
    ProjectExtendedResponseModelQualityPreset,
    ProjectExtendedResponseModelSourceType,
    ProjectExtendedResponseModelTargetAudience,
    ProjectResponse,
    ProjectResponseModelAccessLevel,
    ProjectResponseModelFiction,
    ProjectResponseModelSourceType,
    ProjectResponseModelTargetAudience,
    ProjectSnapshotExtendedResponseModel,
    ProjectSnapshotResponse,
    ProjectSnapshotsResponse,
    ProjectState,
    PromptAgent,
    PromptAgentDbModel,
    PromptAgentDbModelToolsItem,
    PromptAgentDbModelToolsItem_Client,
    PromptAgentDbModelToolsItem_Mcp,
    PromptAgentDbModelToolsItem_System,
    PromptAgentDbModelToolsItem_Webhook,
    PromptAgentInputToolsItem,
    PromptAgentInputToolsItem_Client,
    PromptAgentInputToolsItem_Mcp,
    PromptAgentInputToolsItem_System,
    PromptAgentInputToolsItem_Webhook,
    PromptAgentOutputToolsItem,
    PromptAgentOutputToolsItem_Client,
    PromptAgentOutputToolsItem_Mcp,
    PromptAgentOutputToolsItem_System,
    PromptAgentOutputToolsItem_Webhook,
    PromptAgentOverride,
    PromptAgentOverrideConfig,
    PromptEvaluationCriteria,
    PronunciationDictionaryAliasRuleRequestModel,
    PronunciationDictionaryLocator,
    PronunciationDictionaryLocatorResponseModel,
    PronunciationDictionaryPhonemeRuleRequestModel,
    PronunciationDictionaryRulesResponseModel,
    PronunciationDictionaryVersionLocator,
    PronunciationDictionaryVersionResponseModel,
    PronunciationDictionaryVersionResponseModelPermissionOnResource,
    PydanticPronunciationDictionaryVersionLocator,
    QueryParamsJsonSchema,
    RagChunkMetadata,
    RagConfig,
    RagDocumentIndexResponseModel,
    RagDocumentIndexUsage,
    RagDocumentIndexesResponseModel,
    RagIndexOverviewEmbeddingModelResponseModel,
    RagIndexOverviewResponseModel,
    RagIndexStatus,
    RagRetrievalInfo,
    ReaderResourceResponseModel,
    ReaderResourceResponseModelResourceType,
    RealtimeVoiceSettings,
    RecordingResponse,
    RemoveMemberFromGroupRequest,
    Render,
    RenderStatus,
    RenderType,
    RequestPvcManualVerificationResponseModel,
    ResourceAccessInfo,
    ResourceAccessInfoRole,
    ResourceMetadataResponseModel,
    ReviewStatus,
    SafetyCommonModel,
    SafetyEvaluation,
    SafetyResponseModel,
    SafetyRule,
    SecretDependencyType,
    SegmentCreateResponse,
    SegmentDeleteResponse,
    SegmentDubResponse,
    SegmentTranscriptionResponse,
    SegmentTranslationResponse,
    SegmentUpdateResponse,
    SegmentedJsonExportOptions,
    SendText,
    SendTextMulti,
    ShareOptionResponseModel,
    ShareOptionResponseModelType,
    SimilarVoice,
    SimilarVoiceCategory,
    SimilarVoicesForSpeakerResponse,
    SipMediaEncryptionEnum,
    SipTrunkConfigResponseModel,
    SipTrunkCredentials,
    SipTrunkOutboundCallResponse,
    SipTrunkTransportEnum,
    SkipTurnToolConfig,
    SpeakerAudioResponseModel,
    SpeakerResponseModel,
    SpeakerSegment,
    SpeakerSeparationResponseModel,
    SpeakerSeparationResponseModelStatus,
    SpeakerTrack,
    SpeakerUpdatedResponse,
    SpeechHistoryItemResponse,
    SpeechHistoryItemResponseModelSource,
    SpeechHistoryItemResponseModelVoiceCategory,
    SpeechToTextCharacterResponseModel,
    SpeechToTextChunkResponseModel,
    SpeechToTextWordResponseModel,
    SpeechToTextWordResponseModelType,
    SrtExportOptions,
    StartPvcVoiceTrainingResponseModel,
    StartSpeakerSeparationResponseModel,
    StreamingAudioChunkWithTimestampsResponse,
    Subscription,
    SubscriptionExtrasResponseModel,
    SubscriptionResponse,
    SubscriptionResponseModelBillingPeriod,
    SubscriptionResponseModelCharacterRefreshPeriod,
    SubscriptionResponseModelCurrency,
    SubscriptionStatusType,
    SubscriptionUsageResponseModel,
    SupportedVoice,
    SystemToolConfigInput,
    SystemToolConfigInputParams,
    SystemToolConfigInputParams_EndCall,
    SystemToolConfigInputParams_LanguageDetection,
    SystemToolConfigInputParams_SkipTurn,
    SystemToolConfigInputParams_TransferToAgent,
    SystemToolConfigInputParams_TransferToNumber,
    SystemToolConfigOutput,
    SystemToolConfigOutputParams,
    SystemToolConfigOutputParams_EndCall,
    SystemToolConfigOutputParams_LanguageDetection,
    SystemToolConfigOutputParams_SkipTurn,
    SystemToolConfigOutputParams_TransferToAgent,
    SystemToolConfigOutputParams_TransferToNumber,
    TelephonyProvider,
    TextToSpeechApplyTextNormalizationEnum,
    TextToSpeechOutputFormatEnum,
    TextToSpeechStreamRequest,
    Tool,
    ToolAnnotations,
    ToolMockConfig,
    TransferToAgentToolConfig,
    TransferToNumberToolConfig,
    TtsConversationalConfigInput,
    TtsConversationalConfigOutput,
    TtsConversationalConfigOverride,
    TtsConversationalConfigOverrideConfig,
    TtsConversationalModel,
    TtsModelFamily,
    TtsOptimizeStreamingLatency,
    TtsOutputFormat,
    TurnConfig,
    TurnMode,
    TwilioOutboundCallResponse,
    TxtExportOptions,
    UpdateAudioNativeProjectRequest,
    UpdateChapterRequest,
    UpdateProjectRequest,
    UpdatePronunciationDictionariesRequest,
    UpdateWorkspaceMemberResponseModel,
    UrlAvatar,
    UsageAggregationInterval,
    UsageCharactersResponseModel,
    User,
    UserFeedback,
    UserFeedbackScore,
    UtteranceResponseModel,
    ValidationError,
    ValidationErrorLocItem,
    VerificationAttemptResponse,
    VerifiedVoiceLanguageResponseModel,
    VerifyPvcVoiceCaptchaResponseModel,
    Voice,
    VoiceDesignPreviewResponse,
    VoiceGenerationParameterOptionResponse,
    VoiceGenerationParameterResponse,
    VoicePreviewResponseModel,
    VoiceResponseModelCategory,
    VoiceResponseModelSafetyControl,
    VoiceSample,
    VoiceSamplePreviewResponseModel,
    VoiceSampleVisualWaveformResponseModel,
    VoiceSettings,
    VoiceSharingModerationCheckResponseModel,
    VoiceSharingResponse,
    VoiceSharingResponseModelCategory,
    VoiceSharingState,
    VoiceVerificationResponse,
    WebhookAuthMethodType,
    WebhookToolApiSchemaConfigInput,
    WebhookToolApiSchemaConfigInputMethod,
    WebhookToolApiSchemaConfigInputRequestHeadersValue,
    WebhookToolApiSchemaConfigOutput,
    WebhookToolApiSchemaConfigOutputMethod,
    WebhookToolApiSchemaConfigOutputRequestHeadersValue,
    WebhookToolConfigInput,
    WebhookToolConfigOutput,
    WebhookUsageType,
    WebsocketTtsClientMessageMulti,
    WebsocketTtsServerMessageMulti,
    WidgetConfig,
    WidgetConfigInputAvatar,
    WidgetConfigInputAvatar_Image,
    WidgetConfigInputAvatar_Orb,
    WidgetConfigInputAvatar_Url,
    WidgetConfigOutputAvatar,
    WidgetConfigOutputAvatar_Image,
    WidgetConfigOutputAvatar_Orb,
    WidgetConfigOutputAvatar_Url,
    WidgetConfigResponse,
    WidgetConfigResponseModelAvatar,
    WidgetConfigResponseModelAvatar_Image,
    WidgetConfigResponseModelAvatar_Orb,
    WidgetConfigResponseModelAvatar_Url,
    WidgetExpandable,
    WidgetFeedbackMode,
    WidgetLanguagePreset,
    WidgetLanguagePresetResponse,
    WidgetPlacement,
    WidgetStyles,
    WidgetTextContents,
    WorkspaceBatchCallsResponse,
    WorkspaceGroupByNameResponseModel,
    WorkspaceResourceType,
    WorkspaceWebhookListResponseModel,
    WorkspaceWebhookResponseModel,
    WorkspaceWebhookUsageResponseModel,
)
from .errors import BadRequestError, ForbiddenError, NotFoundError, TooEarlyError, UnprocessableEntityError
from . import (
    audio_isolation,
    audio_native,
    conversational_ai,
    dubbing,
    forced_alignment,
    history,
    models,
    pronunciation_dictionaries,
    samples,
    speech_to_speech,
    speech_to_text,
    studio,
    text_to_dialogue,
    text_to_sound_effects,
    text_to_speech,
    text_to_voice,
    usage,
    user,
    v_1_text_to_speech_voice_id_multi_stream_input,
    v_1_text_to_speech_voice_id_stream_input,
    voices,
    webhooks,
    workspace,
)
from .audio_isolation import AudioIsolationConvertRequestFileFormat, AudioIsolationStreamRequestFileFormat
from .client import AsyncElevenLabs, ElevenLabs
from .environment import ElevenLabsEnvironment
from .history import HistoryListRequestSource
from .play import play, save, stream
from .pronunciation_dictionaries import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
    PronunciationDictionariesCreateFromFileRequestWorkspaceAccess,
    PronunciationDictionariesListRequestSort,
)
from .speech_to_speech import (
    SpeechToSpeechConvertRequestFileFormat,
    SpeechToSpeechConvertRequestOutputFormat,
    SpeechToSpeechStreamRequestFileFormat,
    SpeechToSpeechStreamRequestOutputFormat,
)
from .speech_to_text import SpeechToTextConvertRequestFileFormat, SpeechToTextConvertRequestTimestampsGranularity
from .studio import (
    BodyCreatePodcastV1StudioPodcastsPostDurationScale,
    BodyCreatePodcastV1StudioPodcastsPostMode,
    BodyCreatePodcastV1StudioPodcastsPostMode_Bulletin,
    BodyCreatePodcastV1StudioPodcastsPostMode_Conversation,
    BodyCreatePodcastV1StudioPodcastsPostQualityPreset,
    BodyCreatePodcastV1StudioPodcastsPostSource,
    BodyCreatePodcastV1StudioPodcastsPostSourceItem,
    BodyCreatePodcastV1StudioPodcastsPostSourceItem_Text,
    BodyCreatePodcastV1StudioPodcastsPostSourceItem_Url,
)
from .text_to_dialogue import TextToDialogueConvertRequestOutputFormat, TextToDialogueStreamRequestOutputFormat
from .text_to_sound_effects import TextToSoundEffectsConvertRequestOutputFormat
from .text_to_speech import (
    BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization,
    BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization,
    BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization,
    BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization,
    TextToSpeechConvertRequestOutputFormat,
    TextToSpeechConvertWithTimestampsRequestOutputFormat,
    TextToSpeechStreamRequestOutputFormat,
    TextToSpeechStreamWithTimestampsRequestOutputFormat,
)
from .text_to_voice import TextToVoiceCreatePreviewsRequestOutputFormat
from .v_1_text_to_speech_voice_id_multi_stream_input import ReceiveMessageMulti, SendMessageMulti
from .v_1_text_to_speech_voice_id_stream_input import ReceiveMessage, SendMessage
from .version import __version__
from .voices import VoicesGetSharedRequestCategory

__all__ = [
    "AddChapterResponseModel",
    "AddKnowledgeBaseResponseModel",
    "AddProjectRequest",
    "AddProjectResponseModel",
    "AddPronunciationDictionaryResponseModel",
    "AddPronunciationDictionaryResponseModelPermissionOnResource",
    "AddSharingVoiceRequest",
    "AddVoiceIvcResponseModel",
    "AddVoiceResponseModel",
    "AddWorkspaceGroupMemberResponseModel",
    "AddWorkspaceInviteResponseModel",
    "AdditionalFormatResponseModel",
    "AdditionalFormats",
    "Age",
    "AgentBan",
    "AgentCallLimits",
    "AgentConfig",
    "AgentConfigOverride",
    "AgentConfigOverrideConfig",
    "AgentMetadataResponseModel",
    "AgentPlatformSettingsRequestModel",
    "AgentPlatformSettingsResponseModel",
    "AgentSimulatedChatTestResponseModel",
    "AgentSummaryResponseModel",
    "AgentTransfer",
    "AgentWorkspaceOverridesInput",
    "AgentWorkspaceOverridesOutput",
    "Alignment",
    "AllowlistItem",
    "ArrayJsonSchemaPropertyInput",
    "ArrayJsonSchemaPropertyInputItems",
    "ArrayJsonSchemaPropertyOutput",
    "ArrayJsonSchemaPropertyOutputItems",
    "AsrConversationalConfig",
    "AsrInputFormat",
    "AsrProvider",
    "AsrQuality",
    "AsyncElevenLabs",
    "AudioIsolationConvertRequestFileFormat",
    "AudioIsolationStreamRequestFileFormat",
    "AudioNativeCreateProjectResponseModel",
    "AudioNativeEditContentResponseModel",
    "AudioNativeProjectSettingsResponseModel",
    "AudioNativeProjectSettingsResponseModelStatus",
    "AudioOutput",
    "AudioOutputMulti",
    "AudioWithTimestampsResponse",
    "AuthSettings",
    "AuthorizationMethod",
    "BadRequestError",
    "BanReasonType",
    "BatchCallDetailedResponse",
    "BatchCallRecipientStatus",
    "BatchCallResponse",
    "BatchCallStatus",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess",
    "BodyAddChapterToAProjectV1ProjectsProjectIdChaptersAddPost",
    "BodyAddProjectV1ProjectsAddPostApplyTextNormalization",
    "BodyAddProjectV1ProjectsAddPostFiction",
    "BodyAddProjectV1ProjectsAddPostSourceType",
    "BodyAddProjectV1ProjectsAddPostTargetAudience",
    "BodyAddToKnowledgeBaseV1ConvaiAddToKnowledgeBasePost",
    "BodyAddToKnowledgeBaseV1ConvaiAgentsAgentIdAddToKnowledgeBasePost",
    "BodyCreatePodcastV1ProjectsPodcastCreatePost",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostMode",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Bulletin",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostMode_Conversation",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSource",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Text",
    "BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Url",
    "BodyCreatePodcastV1StudioPodcastsPostDurationScale",
    "BodyCreatePodcastV1StudioPodcastsPostMode",
    "BodyCreatePodcastV1StudioPodcastsPostMode_Bulletin",
    "BodyCreatePodcastV1StudioPodcastsPostMode_Conversation",
    "BodyCreatePodcastV1StudioPodcastsPostQualityPreset",
    "BodyCreatePodcastV1StudioPodcastsPostSource",
    "BodyCreatePodcastV1StudioPodcastsPostSourceItem",
    "BodyCreatePodcastV1StudioPodcastsPostSourceItem_Text",
    "BodyCreatePodcastV1StudioPodcastsPostSourceItem_Url",
    "BodyEditBasicProjectInfoV1ProjectsProjectIdPost",
    "BodyEditChapterV1ProjectsProjectIdChaptersChapterIdPatch",
    "BodyEditProjectContentV1ProjectsProjectIdContentPost",
    "BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge",
    "BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender",
    "BodyRetrieveVoiceSampleAudioV1VoicesPvcVoiceIdSamplesSampleIdAudioGet",
    "BodyStreamChapterAudioV1ProjectsProjectIdChaptersChapterIdSnapshotsChapterSnapshotIdStreamPost",
    "BodyStreamProjectAudioV1ProjectsProjectIdSnapshotsProjectSnapshotIdStreamPost",
    "BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization",
    "BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization",
    "BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization",
    "BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization",
    "BreakdownTypes",
    "BuiltInTools",
    "ChapterContentBlockExtendableNodeResponseModel",
    "ChapterContentBlockInputModel",
    "ChapterContentBlockInputModelSubType",
    "ChapterContentBlockResponseModel",
    "ChapterContentBlockResponseModelNodesItem",
    "ChapterContentBlockResponseModelNodesItem_Other",
    "ChapterContentBlockResponseModelNodesItem_TtsNode",
    "ChapterContentBlockTtsNodeResponseModel",
    "ChapterContentInputModel",
    "ChapterContentParagraphTtsNodeInputModel",
    "ChapterContentResponseModel",
    "ChapterResponse",
    "ChapterSnapshotExtendedResponseModel",
    "ChapterSnapshotResponse",
    "ChapterSnapshotsResponse",
    "ChapterState",
    "ChapterStatisticsResponse",
    "ChapterWithContentResponseModel",
    "ChapterWithContentResponseModelState",
    "CharacterAlignmentModel",
    "CharacterAlignmentResponseModel",
    "CharacterUsageResponse",
    "ClientEvent",
    "ClientToolConfigInput",
    "ClientToolConfigOutput",
    "CloseConnection",
    "CloseContext",
    "CloseSocket",
    "ConvAiDynamicVariable",
    "ConvAiSecretLocator",
    "ConvAiStoredSecretDependencies",
    "ConvAiStoredSecretDependenciesAgentToolsItem",
    "ConvAiStoredSecretDependenciesAgentToolsItem_Available",
    "ConvAiStoredSecretDependenciesAgentToolsItem_Unknown",
    "ConvAiStoredSecretDependenciesToolsItem",
    "ConvAiStoredSecretDependenciesToolsItem_Available",
    "ConvAiStoredSecretDependenciesToolsItem_Unknown",
    "ConvAiUserSecretDbModel",
    "ConvAiWebhooks",
    "ConvAiWorkspaceStoredSecretConfig",
    "ConversationChargingCommonModel",
    "ConversationConfig",
    "ConversationConfigClientOverrideConfigInput",
    "ConversationConfigClientOverrideConfigOutput",
    "ConversationConfigClientOverrideInput",
    "ConversationConfigClientOverrideOutput",
    "ConversationConfigOverride",
    "ConversationConfigOverrideConfig",
    "ConversationDeletionSettings",
    "ConversationHistoryAnalysisCommonModel",
    "ConversationHistoryBatchCallModel",
    "ConversationHistoryErrorCommonModel",
    "ConversationHistoryEvaluationCriteriaResultCommonModel",
    "ConversationHistoryFeedbackCommonModel",
    "ConversationHistoryMetadataCommonModel",
    "ConversationHistoryMetadataCommonModelPhoneCall",
    "ConversationHistoryMetadataCommonModelPhoneCall_SipTrunking",
    "ConversationHistoryMetadataCommonModelPhoneCall_Twilio",
    "ConversationHistoryRagUsageCommonModel",
    "ConversationHistorySipTrunkingPhoneCallModel",
    "ConversationHistorySipTrunkingPhoneCallModelDirection",
    "ConversationHistoryTranscriptCommonModelInput",
    "ConversationHistoryTranscriptCommonModelInputRole",
    "ConversationHistoryTranscriptCommonModelInputSourceMedium",
    "ConversationHistoryTranscriptCommonModelOutput",
    "ConversationHistoryTranscriptCommonModelOutputRole",
    "ConversationHistoryTranscriptCommonModelOutputSourceMedium",
    "ConversationHistoryTranscriptToolCallClientDetails",
    "ConversationHistoryTranscriptToolCallCommonModel",
    "ConversationHistoryTranscriptToolCallCommonModelToolDetails",
    "ConversationHistoryTranscriptToolCallCommonModelToolDetails_Client",
    "ConversationHistoryTranscriptToolCallCommonModelToolDetails_Webhook",
    "ConversationHistoryTranscriptToolCallWebhookDetails",
    "ConversationHistoryTranscriptToolResultCommonModel",
    "ConversationHistoryTwilioPhoneCallModel",
    "ConversationHistoryTwilioPhoneCallModelDirection",
    "ConversationInitiationClientDataConfigInput",
    "ConversationInitiationClientDataConfigOutput",
    "ConversationInitiationClientDataInternal",
    "ConversationInitiationClientDataInternalDynamicVariablesValue",
    "ConversationInitiationClientDataRequestInput",
    "ConversationInitiationClientDataRequestInputDynamicVariablesValue",
    "ConversationInitiationClientDataRequestOutput",
    "ConversationInitiationClientDataRequestOutputDynamicVariablesValue",
    "ConversationInitiationClientDataWebhook",
    "ConversationInitiationClientDataWebhookRequestHeadersValue",
    "ConversationSignedUrlResponseModel",
    "ConversationSimulationSpecification",
    "ConversationSimulationSpecificationDynamicVariablesValue",
    "ConversationSummaryResponseModel",
    "ConversationSummaryResponseModelStatus",
    "ConversationTokenDbModel",
    "ConversationTokenPurpose",
    "ConversationTurnMetrics",
    "ConversationalConfig",
    "ConvertChapterResponseModel",
    "ConvertProjectResponseModel",
    "CreateAgentResponseModel",
    "CreateAudioNativeProjectRequest",
    "CreatePhoneNumberResponseModel",
    "CreatePreviouslyGeneratedVoiceRequest",
    "CreatePronunciationDictionaryResponseModel",
    "CreateSipTrunkPhoneNumberRequest",
    "CreateTranscriptRequest",
    "CreateTwilioPhoneNumberRequest",
    "CustomLlm",
    "CustomLlmRequestHeadersValue",
    "DashboardCallSuccessChartModel",
    "DashboardCriteriaChartModel",
    "DashboardDataCollectionChartModel",
    "DataCollectionResultCommonModel",
    "DeleteChapterRequest",
    "DeleteChapterResponseModel",
    "DeleteDubbingResponseModel",
    "DeleteHistoryItemResponse",
    "DeleteProjectRequest",
    "DeleteProjectResponseModel",
    "DeleteSampleResponse",
    "DeleteVoiceResponseModel",
    "DeleteVoiceSampleResponseModel",
    "DeleteWorkspaceGroupMemberResponseModel",
    "DeleteWorkspaceInviteResponseModel",
    "DeleteWorkspaceMemberResponseModel",
    "DependentAvailableAgentIdentifier",
    "DependentAvailableAgentIdentifierAccessLevel",
    "DependentAvailableAgentToolIdentifier",
    "DependentAvailableAgentToolIdentifierAccessLevel",
    "DependentAvailableToolIdentifier",
    "DependentAvailableToolIdentifierAccessLevel",
    "DependentPhoneNumberIdentifier",
    "DependentUnknownAgentIdentifier",
    "DependentUnknownAgentToolIdentifier",
    "DependentUnknownToolIdentifier",
    "DialogueInput",
    "DialogueInputResponseModel",
    "DoDubbingResponse",
    "DocumentUsageModeEnum",
    "DocxExportOptions",
    "DubbedSegment",
    "DubbingMediaMetadata",
    "DubbingMediaReference",
    "DubbingMetadataResponse",
    "DubbingRenderResponseModel",
    "DubbingResource",
    "DynamicVariablesConfig",
    "DynamicVariablesConfigDynamicVariablePlaceholdersValue",
    "EditChapterResponseModel",
    "EditProjectResponseModel",
    "EditVoiceResponseModel",
    "EditVoiceSettingsRequest",
    "EditVoiceSettingsResponseModel",
    "ElevenLabs",
    "ElevenLabsEnvironment",
    "EmbedVariant",
    "EmbeddingModelEnum",
    "EndCallToolConfig",
    "EvaluationSettings",
    "EvaluationSuccessResult",
    "ExportOptions",
    "ExportOptions_Docx",
    "ExportOptions_Html",
    "ExportOptions_Pdf",
    "ExportOptions_SegmentedJson",
    "ExportOptions_Srt",
    "ExportOptions_Txt",
    "ExtendedSubscriptionResponseModelBillingPeriod",
    "ExtendedSubscriptionResponseModelCharacterRefreshPeriod",
    "ExtendedSubscriptionResponseModelCurrency",
    "FeatureStatusCommonModel",
    "FeaturesUsageCommonModel",
    "FeedbackItem",
    "FinalOutput",
    "FinalOutputMulti",
    "FineTuningResponse",
    "FineTuningResponseModelStateValue",
    "FlushContext",
    "ForbiddenError",
    "ForcedAlignmentCharacterResponseModel",
    "ForcedAlignmentResponseModel",
    "ForcedAlignmentWordResponseModel",
    "Gender",
    "GenerateVoiceRequest",
    "GenerationConfig",
    "GetAgentEmbedResponseModel",
    "GetAgentKnowledgebaseSizeResponseModel",
    "GetAgentLinkResponseModel",
    "GetAgentResponseModel",
    "GetAgentResponseModelPhoneNumbersItem",
    "GetAgentResponseModelPhoneNumbersItem_SipTrunk",
    "GetAgentResponseModelPhoneNumbersItem_Twilio",
    "GetAgentsPageResponseModel",
    "GetAudioNativeProjectSettingsResponseModel",
    "GetChapterRequest",
    "GetChapterSnapshotsRequest",
    "GetChaptersRequest",
    "GetChaptersResponse",
    "GetConvAiDashboardSettingsResponseModel",
    "GetConvAiDashboardSettingsResponseModelChartsItem",
    "GetConvAiDashboardSettingsResponseModelChartsItem_CallSuccess",
    "GetConvAiDashboardSettingsResponseModelChartsItem_Criteria",
    "GetConvAiDashboardSettingsResponseModelChartsItem_DataCollection",
    "GetConvAiSettingsResponseModel",
    "GetConversationResponseModel",
    "GetConversationResponseModelStatus",
    "GetConversationsPageResponseModel",
    "GetKnowledgeBaseDependentAgentsResponseModel",
    "GetKnowledgeBaseDependentAgentsResponseModelAgentsItem",
    "GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Available",
    "GetKnowledgeBaseDependentAgentsResponseModelAgentsItem_Unknown",
    "GetKnowledgeBaseFileResponseModel",
    "GetKnowledgeBaseListResponseModel",
    "GetKnowledgeBaseListResponseModelDocumentsItem",
    "GetKnowledgeBaseListResponseModelDocumentsItem_File",
    "GetKnowledgeBaseListResponseModelDocumentsItem_Text",
    "GetKnowledgeBaseListResponseModelDocumentsItem_Url",
    "GetKnowledgeBaseSummaryFileResponseModel",
    "GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem",
    "GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Available",
    "GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Unknown",
    "GetKnowledgeBaseSummaryTextResponseModel",
    "GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem",
    "GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Available",
    "GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem_Unknown",
    "GetKnowledgeBaseSummaryUrlResponseModel",
    "GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem",
    "GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Available",
    "GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem_Unknown",
    "GetKnowledgeBaseTextResponseModel",
    "GetKnowledgeBaseUrlResponseModel",
    "GetLibraryVoicesResponse",
    "GetPhoneNumberResponse",
    "GetPhoneNumberSipTrunkResponseModel",
    "GetPhoneNumberTwilioResponseModel",
    "GetProjectRequest",
    "GetProjectsRequest",
    "GetProjectsResponse",
    "GetPronunciationDictionariesMetadataResponseModel",
    "GetPronunciationDictionariesResponse",
    "GetPronunciationDictionaryMetadataResponse",
    "GetPronunciationDictionaryMetadataResponseModelPermissionOnResource",
    "GetPronunciationDictionaryResponse",
    "GetSpeechHistoryResponse",
    "GetVoicesResponse",
    "GetVoicesV2Response",
    "GetWorkspaceSecretsResponseModel",
    "HistoryAlignmentResponseModel",
    "HistoryAlignmentsResponseModel",
    "HistoryItemResponse",
    "HistoryListRequestSource",
    "HtmlExportOptions",
    "HttpValidationError",
    "ImageAvatar",
    "InitialiseContext",
    "InitializeConnection",
    "InitializeConnectionMulti",
    "IntegrationType",
    "InvoiceResponse",
    "KeepContextAlive",
    "KnowledgeBaseDocumentChunkResponseModel",
    "KnowledgeBaseDocumentMetadataResponseModel",
    "KnowledgeBaseDocumentType",
    "KnowledgeBaseLocator",
    "LanguageAddedResponse",
    "LanguageDetectionToolConfig",
    "LanguagePresetInput",
    "LanguagePresetOutput",
    "LanguagePresetTranslation",
    "LanguageResponse",
    "LibraryVoiceResponse",
    "LibraryVoiceResponseModelCategory",
    "ListMcpToolsResponseModel",
    "LiteralJsonSchemaProperty",
    "LiteralJsonSchemaPropertyConstantValue",
    "LiteralJsonSchemaPropertyType",
    "Llm",
    "LlmCategoryUsage",
    "LlmInputOutputTokensUsage",
    "LlmTokensCategoryUsage",
    "LlmUsageCalculatorLlmResponseModel",
    "LlmUsageCalculatorResponseModel",
    "LlmUsageInput",
    "LlmUsageOutput",
    "ManualVerificationFileResponse",
    "ManualVerificationResponse",
    "McpApprovalPolicy",
    "McpServerConfigInput",
    "McpServerConfigInputRequestHeadersValue",
    "McpServerConfigInputSecretToken",
    "McpServerConfigInputUrl",
    "McpServerConfigOutput",
    "McpServerConfigOutputRequestHeadersValue",
    "McpServerConfigOutputSecretToken",
    "McpServerConfigOutputUrl",
    "McpServerMetadataResponseModel",
    "McpServerResponseModel",
    "McpServerResponseModelDependentAgentsItem",
    "McpServerResponseModelDependentAgentsItem_Available",
    "McpServerResponseModelDependentAgentsItem_Unknown",
    "McpServerTransport",
    "McpServersResponseModel",
    "McpToolApprovalHash",
    "McpToolApprovalPolicy",
    "McpToolConfigInput",
    "McpToolConfigOutput",
    "MetricRecord",
    "MetricType",
    "Model",
    "ModelRatesResponseModel",
    "ModelResponseModelConcurrencyGroup",
    "ModelSettingsResponseModel",
    "ModerationStatusResponseModel",
    "ModerationStatusResponseModelSafetyStatus",
    "ModerationStatusResponseModelWarningStatus",
    "NormalizedAlignment",
    "NotFoundError",
    "ObjectJsonSchemaPropertyInput",
    "ObjectJsonSchemaPropertyInputPropertiesValue",
    "ObjectJsonSchemaPropertyOutput",
    "ObjectJsonSchemaPropertyOutputPropertiesValue",
    "OrbAvatar",
    "OutboundCallRecipient",
    "OutboundCallRecipientResponseModel",
    "OutputFormat",
    "PdfExportOptions",
    "PhoneNumberAgentInfo",
    "PhoneNumberTransfer",
    "PodcastBulletinMode",
    "PodcastBulletinModeData",
    "PodcastConversationMode",
    "PodcastConversationModeData",
    "PodcastProjectResponseModel",
    "PodcastTextSource",
    "PodcastUrlSource",
    "PostAgentAvatarResponseModel",
    "PostWorkspaceSecretResponseModel",
    "PrivacyConfig",
    "ProjectCreationMetaResponseModel",
    "ProjectCreationMetaResponseModelStatus",
    "ProjectCreationMetaResponseModelType",
    "ProjectExtendedResponse",
    "ProjectExtendedResponseModelAccessLevel",
    "ProjectExtendedResponseModelApplyTextNormalization",
    "ProjectExtendedResponseModelFiction",
    "ProjectExtendedResponseModelQualityPreset",
    "ProjectExtendedResponseModelSourceType",
    "ProjectExtendedResponseModelTargetAudience",
    "ProjectResponse",
    "ProjectResponseModelAccessLevel",
    "ProjectResponseModelFiction",
    "ProjectResponseModelSourceType",
    "ProjectResponseModelTargetAudience",
    "ProjectSnapshotExtendedResponseModel",
    "ProjectSnapshotResponse",
    "ProjectSnapshotsResponse",
    "ProjectState",
    "PromptAgent",
    "PromptAgentDbModel",
    "PromptAgentDbModelToolsItem",
    "PromptAgentDbModelToolsItem_Client",
    "PromptAgentDbModelToolsItem_Mcp",
    "PromptAgentDbModelToolsItem_System",
    "PromptAgentDbModelToolsItem_Webhook",
    "PromptAgentInputToolsItem",
    "PromptAgentInputToolsItem_Client",
    "PromptAgentInputToolsItem_Mcp",
    "PromptAgentInputToolsItem_System",
    "PromptAgentInputToolsItem_Webhook",
    "PromptAgentOutputToolsItem",
    "PromptAgentOutputToolsItem_Client",
    "PromptAgentOutputToolsItem_Mcp",
    "PromptAgentOutputToolsItem_System",
    "PromptAgentOutputToolsItem_Webhook",
    "PromptAgentOverride",
    "PromptAgentOverrideConfig",
    "PromptEvaluationCriteria",
    "PronunciationDictionariesCreateFromFileRequestWorkspaceAccess",
    "PronunciationDictionariesListRequestSort",
    "PronunciationDictionaryAliasRuleRequestModel",
    "PronunciationDictionaryLocator",
    "PronunciationDictionaryLocatorResponseModel",
    "PronunciationDictionaryPhonemeRuleRequestModel",
    "PronunciationDictionaryRulesResponseModel",
    "PronunciationDictionaryVersionLocator",
    "PronunciationDictionaryVersionResponseModel",
    "PronunciationDictionaryVersionResponseModelPermissionOnResource",
    "PydanticPronunciationDictionaryVersionLocator",
    "QueryParamsJsonSchema",
    "RagChunkMetadata",
    "RagConfig",
    "RagDocumentIndexResponseModel",
    "RagDocumentIndexUsage",
    "RagDocumentIndexesResponseModel",
    "RagIndexOverviewEmbeddingModelResponseModel",
    "RagIndexOverviewResponseModel",
    "RagIndexStatus",
    "RagRetrievalInfo",
    "ReaderResourceResponseModel",
    "ReaderResourceResponseModelResourceType",
    "RealtimeVoiceSettings",
    "ReceiveMessage",
    "ReceiveMessageMulti",
    "RecordingResponse",
    "RemoveMemberFromGroupRequest",
    "Render",
    "RenderStatus",
    "RenderType",
    "RequestPvcManualVerificationResponseModel",
    "ResourceAccessInfo",
    "ResourceAccessInfoRole",
    "ResourceMetadataResponseModel",
    "ReviewStatus",
    "SafetyCommonModel",
    "SafetyEvaluation",
    "SafetyResponseModel",
    "SafetyRule",
    "SecretDependencyType",
    "SegmentCreateResponse",
    "SegmentDeleteResponse",
    "SegmentDubResponse",
    "SegmentTranscriptionResponse",
    "SegmentTranslationResponse",
    "SegmentUpdateResponse",
    "SegmentedJsonExportOptions",
    "SendMessage",
    "SendMessageMulti",
    "SendText",
    "SendTextMulti",
    "ShareOptionResponseModel",
    "ShareOptionResponseModelType",
    "SimilarVoice",
    "SimilarVoiceCategory",
    "SimilarVoicesForSpeakerResponse",
    "SipMediaEncryptionEnum",
    "SipTrunkConfigResponseModel",
    "SipTrunkCredentials",
    "SipTrunkOutboundCallResponse",
    "SipTrunkTransportEnum",
    "SkipTurnToolConfig",
    "SpeakerAudioResponseModel",
    "SpeakerResponseModel",
    "SpeakerSegment",
    "SpeakerSeparationResponseModel",
    "SpeakerSeparationResponseModelStatus",
    "SpeakerTrack",
    "SpeakerUpdatedResponse",
    "SpeechHistoryItemResponse",
    "SpeechHistoryItemResponseModelSource",
    "SpeechHistoryItemResponseModelVoiceCategory",
    "SpeechToSpeechConvertRequestFileFormat",
    "SpeechToSpeechConvertRequestOutputFormat",
    "SpeechToSpeechStreamRequestFileFormat",
    "SpeechToSpeechStreamRequestOutputFormat",
    "SpeechToTextCharacterResponseModel",
    "SpeechToTextChunkResponseModel",
    "SpeechToTextConvertRequestFileFormat",
    "SpeechToTextConvertRequestTimestampsGranularity",
    "SpeechToTextWordResponseModel",
    "SpeechToTextWordResponseModelType",
    "SrtExportOptions",
    "StartPvcVoiceTrainingResponseModel",
    "StartSpeakerSeparationResponseModel",
    "StreamingAudioChunkWithTimestampsResponse",
    "Subscription",
    "SubscriptionExtrasResponseModel",
    "SubscriptionResponse",
    "SubscriptionResponseModelBillingPeriod",
    "SubscriptionResponseModelCharacterRefreshPeriod",
    "SubscriptionResponseModelCurrency",
    "SubscriptionStatusType",
    "SubscriptionUsageResponseModel",
    "SupportedVoice",
    "SystemToolConfigInput",
    "SystemToolConfigInputParams",
    "SystemToolConfigInputParams_EndCall",
    "SystemToolConfigInputParams_LanguageDetection",
    "SystemToolConfigInputParams_SkipTurn",
    "SystemToolConfigInputParams_TransferToAgent",
    "SystemToolConfigInputParams_TransferToNumber",
    "SystemToolConfigOutput",
    "SystemToolConfigOutputParams",
    "SystemToolConfigOutputParams_EndCall",
    "SystemToolConfigOutputParams_LanguageDetection",
    "SystemToolConfigOutputParams_SkipTurn",
    "SystemToolConfigOutputParams_TransferToAgent",
    "SystemToolConfigOutputParams_TransferToNumber",
    "TelephonyProvider",
    "TextToDialogueConvertRequestOutputFormat",
    "TextToDialogueStreamRequestOutputFormat",
    "TextToSoundEffectsConvertRequestOutputFormat",
    "TextToSpeechApplyTextNormalizationEnum",
    "TextToSpeechConvertRequestOutputFormat",
    "TextToSpeechConvertWithTimestampsRequestOutputFormat",
    "TextToSpeechOutputFormatEnum",
    "TextToSpeechStreamRequest",
    "TextToSpeechStreamRequestOutputFormat",
    "TextToSpeechStreamWithTimestampsRequestOutputFormat",
    "TextToVoiceCreatePreviewsRequestOutputFormat",
    "TooEarlyError",
    "Tool",
    "ToolAnnotations",
    "ToolMockConfig",
    "TransferToAgentToolConfig",
    "TransferToNumberToolConfig",
    "TtsConversationalConfigInput",
    "TtsConversationalConfigOutput",
    "TtsConversationalConfigOverride",
    "TtsConversationalConfigOverrideConfig",
    "TtsConversationalModel",
    "TtsModelFamily",
    "TtsOptimizeStreamingLatency",
    "TtsOutputFormat",
    "TurnConfig",
    "TurnMode",
    "TwilioOutboundCallResponse",
    "TxtExportOptions",
    "UnprocessableEntityError",
    "UpdateAudioNativeProjectRequest",
    "UpdateChapterRequest",
    "UpdateProjectRequest",
    "UpdatePronunciationDictionariesRequest",
    "UpdateWorkspaceMemberResponseModel",
    "UrlAvatar",
    "UsageAggregationInterval",
    "UsageCharactersResponseModel",
    "User",
    "UserFeedback",
    "UserFeedbackScore",
    "UtteranceResponseModel",
    "ValidationError",
    "ValidationErrorLocItem",
    "VerificationAttemptResponse",
    "VerifiedVoiceLanguageResponseModel",
    "VerifyPvcVoiceCaptchaResponseModel",
    "Voice",
    "VoiceDesignPreviewResponse",
    "VoiceGenerationParameterOptionResponse",
    "VoiceGenerationParameterResponse",
    "VoicePreviewResponseModel",
    "VoiceResponseModelCategory",
    "VoiceResponseModelSafetyControl",
    "VoiceSample",
    "VoiceSamplePreviewResponseModel",
    "VoiceSampleVisualWaveformResponseModel",
    "VoiceSettings",
    "VoiceSharingModerationCheckResponseModel",
    "VoiceSharingResponse",
    "VoiceSharingResponseModelCategory",
    "VoiceSharingState",
    "VoiceVerificationResponse",
    "VoicesGetSharedRequestCategory",
    "WebhookAuthMethodType",
    "WebhookToolApiSchemaConfigInput",
    "WebhookToolApiSchemaConfigInputMethod",
    "WebhookToolApiSchemaConfigInputRequestHeadersValue",
    "WebhookToolApiSchemaConfigOutput",
    "WebhookToolApiSchemaConfigOutputMethod",
    "WebhookToolApiSchemaConfigOutputRequestHeadersValue",
    "WebhookToolConfigInput",
    "WebhookToolConfigOutput",
    "WebhookUsageType",
    "WebsocketTtsClientMessageMulti",
    "WebsocketTtsServerMessageMulti",
    "WidgetConfig",
    "WidgetConfigInputAvatar",
    "WidgetConfigInputAvatar_Image",
    "WidgetConfigInputAvatar_Orb",
    "WidgetConfigInputAvatar_Url",
    "WidgetConfigOutputAvatar",
    "WidgetConfigOutputAvatar_Image",
    "WidgetConfigOutputAvatar_Orb",
    "WidgetConfigOutputAvatar_Url",
    "WidgetConfigResponse",
    "WidgetConfigResponseModelAvatar",
    "WidgetConfigResponseModelAvatar_Image",
    "WidgetConfigResponseModelAvatar_Orb",
    "WidgetConfigResponseModelAvatar_Url",
    "WidgetExpandable",
    "WidgetFeedbackMode",
    "WidgetLanguagePreset",
    "WidgetLanguagePresetResponse",
    "WidgetPlacement",
    "WidgetStyles",
    "WidgetTextContents",
    "WorkspaceBatchCallsResponse",
    "WorkspaceGroupByNameResponseModel",
    "WorkspaceResourceType",
    "WorkspaceWebhookListResponseModel",
    "WorkspaceWebhookResponseModel",
    "WorkspaceWebhookUsageResponseModel",
    "__version__",
    "audio_isolation",
    "audio_native",
    "conversational_ai",
    "dubbing",
    "forced_alignment",
    "history",
    "models",
    "play",
    "pronunciation_dictionaries",
    "samples",
    "save",
    "speech_to_speech",
    "speech_to_text",
    "stream",
    "studio",
    "text_to_dialogue",
    "text_to_sound_effects",
    "text_to_speech",
    "text_to_voice",
    "usage",
    "user",
    "v_1_text_to_speech_voice_id_multi_stream_input",
    "v_1_text_to_speech_voice_id_stream_input",
    "voices",
    "webhooks",
    "workspace",
]
