# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.workspace_webhook_list_response_model import WorkspaceWebhookListResponseModel
from .raw_client import Async<PERSON>aw<PERSON>ebhooksClient, RawWebhooksClient


class WebhooksClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawWebhooksClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawWebhooksClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawWebhooksClient
        """
        return self._raw_client

    def list(
        self, *, include_usages: typing.Optional[bool] = None, request_options: typing.Optional[RequestOptions] = None
    ) -> WorkspaceWebhookListResponseModel:
        """
        List all webhooks for a workspace

        Parameters
        ----------
        include_usages : typing.Optional[bool]
            Whether to include active usages of the webhook, only usable by admins

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        WorkspaceWebhookListResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.webhooks.list(
            include_usages=False,
        )
        """
        _response = self._raw_client.list(include_usages=include_usages, request_options=request_options)
        return _response.data


class AsyncWebhooksClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawWebhooksClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawWebhooksClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawWebhooksClient
        """
        return self._raw_client

    async def list(
        self, *, include_usages: typing.Optional[bool] = None, request_options: typing.Optional[RequestOptions] = None
    ) -> WorkspaceWebhookListResponseModel:
        """
        List all webhooks for a workspace

        Parameters
        ----------
        include_usages : typing.Optional[bool]
            Whether to include active usages of the webhook, only usable by admins

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        WorkspaceWebhookListResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.webhooks.list(
                include_usages=False,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(include_usages=include_usages, request_options=request_options)
        return _response.data
