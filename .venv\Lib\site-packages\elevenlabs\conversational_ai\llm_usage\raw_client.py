# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON>NDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import Async<PERSON>ttpR<PERSON>ponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.http_validation_error import HttpValidationError
from ...types.llm_usage_calculator_response_model import LlmUsageCalculatorResponseModel

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawLlmUsageClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def calculate(
        self,
        *,
        prompt_length: int,
        number_of_pages: int,
        rag_enabled: bool,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[LlmUsageCalculatorResponseModel]:
        """
        Returns a list of LLM models and the expected cost for using them based on the provided values.

        Parameters
        ----------
        prompt_length : int
            Length of the prompt in characters.

        number_of_pages : int
            Pages of content in PDF documents or URLs in the agent's knowledge base.

        rag_enabled : bool
            Whether RAG is enabled.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[LlmUsageCalculatorResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/llm-usage/calculate",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "prompt_length": prompt_length,
                "number_of_pages": number_of_pages,
                "rag_enabled": rag_enabled,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    LlmUsageCalculatorResponseModel,
                    construct_type(
                        type_=LlmUsageCalculatorResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawLlmUsageClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def calculate(
        self,
        *,
        prompt_length: int,
        number_of_pages: int,
        rag_enabled: bool,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[LlmUsageCalculatorResponseModel]:
        """
        Returns a list of LLM models and the expected cost for using them based on the provided values.

        Parameters
        ----------
        prompt_length : int
            Length of the prompt in characters.

        number_of_pages : int
            Pages of content in PDF documents or URLs in the agent's knowledge base.

        rag_enabled : bool
            Whether RAG is enabled.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[LlmUsageCalculatorResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/llm-usage/calculate",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "prompt_length": prompt_length,
                "number_of_pages": number_of_pages,
                "rag_enabled": rag_enabled,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    LlmUsageCalculatorResponseModel,
                    construct_type(
                        type_=LlmUsageCalculatorResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
