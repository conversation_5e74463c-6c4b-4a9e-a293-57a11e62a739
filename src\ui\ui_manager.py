import json
import tkinter as tk
import customtkinter as ctk
import os
import sys
import traceback
import threading
from PIL import Image, ImageTk
from deepgram import DeepgramClient  # v4 syntax for the installed version
from deepgram.errors import DeepgramApiKeyError
from tkinter import messagebox

# Import debug_log function from new location
from src.core.logging_utils import debug_log

# Import the new sub-managers - these need to be moved to src/ui/ directory
# For now, importing from root level until they are moved
from ui_theme_manager import UIThemeManager
from ui_bar_manager import UIBarManager
from ui_dropdown_manager import UIDropdownManager
from ui_settings_tab import UISettingsTabManager
from ui_debug_tab import UIDebugTabManager
from src.ui.ui_history_tab import UIHistoryTabManager

ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class UIManager:
    def __init__(self, app_instance):
        self.app = app_instance
        self.dark_mode = True
        self.MONO_FONT_FAMILY = "Consolas"  # Or "Courier New"
        
        # Constants for window sizing
        self.MIN_WINDOW_WIDTH = 630
        self.INITIAL_WINDOW_HEIGHT = 100 
        self.DROPDOWN_VISIBLE_HEIGHT_ADDITION = 615

        # Initialize the theme manager first as other managers depend on its colors
        self.theme_manager = UIThemeManager(self.app, self) 

        # Initialize other UI component managers, passing necessary references
        self.dropdown_manager = UIDropdownManager(self.app, self, self.theme_manager)
        self.bar_manager = UIBarManager(self.app, self, self.theme_manager)
        self.settings_tab_manager = UISettingsTabManager(self.app, self, self.theme_manager)
        self.debug_tab_manager = UIDebugTabManager(self.app, self.theme_manager)
        self.history_tab_manager = UIHistoryTabManager(self.app, self.theme_manager)
        
        # Now that all sub-managers are initialized, setup the UI
        self.setup_ui()

        # Add window resize handler to save size
        self.app.root.bind("<Configure>", self.on_window_resize)
        
    def get_resource_path(self, relative_path):
        try:
            base_path = getattr(sys, '_MEIPASS', os.path.abspath(os.path.dirname(__file__)))
            return os.path.join(base_path, relative_path)
        except Exception:
            return os.path.join(os.path.abspath(os.path.dirname(__file__)), relative_path)
            
    def setup_ui(self):
        if hasattr(self.app, 'ui_initialized') and self.app.ui_initialized:
            debug_log("UIManager setup_ui called but UI already initialized.", "WARN")
            return

        self.app.root.title("Whisp")
        self.app.root.configure(fg_color=self.theme_manager.get_color("bg"))
        self.app.root.minsize(self.MIN_WINDOW_WIDTH, self.INITIAL_WINDOW_HEIGHT)
        
        # Don't override geometry here - the app's smart window system has already set it
        # The window geometry was already configured in _setup_window() before this method
        debug_log("UIManager: Respecting existing window geometry from smart window system", "UI")

        self.main_container = ctk.CTkFrame(self.app.root, fg_color="transparent")
        self.main_container.pack(fill="both", expand=True)

        # Setup main interface (formerly main bar with waveform, gear icon)
        self.bar_manager.setup_main_interface(self.main_container)

        # Setup dropdown panel (settings, logs) - it handles its own packing/unpacking
        self.dropdown_manager.setup_dropdown_panel(self.main_container)
        
        # Delegate tab content setup to the dropdown manager or directly to tab managers
        # The UIDropdownManager.setup_dropdown_panel now creates tab frames.
        # We need to pass these frames to the respective tab managers.
        self.settings_tab_manager.setup_tab(self.dropdown_manager.settings_tab_frame)
        self.history_tab_manager.setup_tab(self.dropdown_manager.history_tab_frame)
        self.debug_tab_manager.setup_tab(self.dropdown_manager.debug_log_tab_frame)

        self.app.ui_initialized = True
        debug_log("Main UIManager: UI setup complete with Main Interface", "UI")
        if self.bar_manager: # Check if bar_manager is initialized
            self.bar_manager.update_status_on_canvas()

        # Notify app that UI is ready
        if hasattr(self.app, 'on_ui_initialized'):
            self.app.on_ui_initialized()

    def toggle_dropdown_panel(self):
        # This method is called by the gear button in UIBaManager
        self.dropdown_manager.toggle_dropdown_panel()

    def refresh_ui_for_theme_change(self):
        debug_log("UIManager: Refreshing UI for theme change.", "UI")
        self.app.root.configure(fg_color=self.theme_manager.get_color("bg"))
        
        if self.bar_manager: self.bar_manager.refresh_theme()
        if self.dropdown_manager: self.dropdown_manager.refresh_theme()
        
        debug_log("UIManager: Full UI theme refresh propagated.", "UI")

    def on_window_resize(self, event):
        if event.widget == self.app.root:
            if self.bar_manager and hasattr(self.bar_manager, 'waveform_canvas') and self.bar_manager.waveform_canvas.winfo_exists():
                if not (hasattr(self.app, 'recording_animation_active') and self.app.recording_animation_active):
                    self.bar_manager._draw_initial_prompt_text()
            
            if hasattr(self.app, '_save_window_dimensions') and hasattr(self.app, 'ui_initialized') and self.app.ui_initialized:
                 pass 

    # --- Methods to be called by app.py or other managers ---
    def update_ui_for_recording_start(self):
        if self.bar_manager: self.bar_manager.update_ui_for_recording_start()

    def update_ui_for_recording_stop(self):
        if self.bar_manager: self.bar_manager.update_ui_for_recording_stop()

    def display_transcription_result(self, transcript_text):
        if self.bar_manager: self.bar_manager.update_status_on_canvas(text_key="transcription_complete", custom_text=transcript_text[:50]+"...") # Show part of transcript
        if hasattr(self.app, 'root') and self.app.root.winfo_exists():
            self.app.root.after(4000, lambda: self.bar_manager.update_status_on_canvas(text_key="initial_prompt") if self.bar_manager else None)

    def display_transcription_error(self, error_message):
        if self.bar_manager: self.bar_manager.update_status_on_canvas(text_key="error", custom_text=error_message)
        if self.debug_tab_manager: self.debug_tab_manager.update_transcription_log(f"ERROR: {error_message}", "ERROR")
        if hasattr(self.app, 'root') and self.app.root.winfo_exists():
            self.app.root.after(5000, lambda: self.bar_manager.update_status_on_canvas(text_key="initial_prompt") if self.bar_manager else None)

    def update_main_status_message(self, message: str, status_type: str = "INFO"):
        # status_type can be INFO, ERROR, WARNING, SUCCESS, RECORDING, PROCESSING
        debug_log(f"UIManager.update_main_status_message: '{message}', type: '{status_type}'", "UI")
        
        # Map status_type to a text_key for bar_manager, and provide custom_text
        text_key_map = {
            "INFO": "custom_info_message",
            "ERROR": "custom_error_message",
            "WARNING": "custom_warning_message",
            "SUCCESS": "custom_success_message",
            "RECORDING": "recording", # Bar manager handles "Recording..." text
            "PROCESSING": "processing" # Bar manager handles "Processing..." text
        }
        text_key_to_use = text_key_map.get(status_type.upper(), "custom_info_message")

        if self.bar_manager:
            self.bar_manager.update_status_on_canvas(text_key=text_key_to_use, custom_text=message)
        
        # Also log important messages to the debug tab
        if status_type.upper() in ["ERROR", "WARNING"] or "error" in message.lower():
             if self.debug_tab_manager: self.debug_tab_manager.update_transcription_log(f"Status ({status_type}): {message}", status_type.upper())
        elif self.debug_tab_manager : # Log other non-error/warning statuses as INFO
             if self.debug_tab_manager: self.debug_tab_manager.update_transcription_log(f"Status ({status_type}): {message}", "INFO")

    def route_log_to_debug_tab(self, message, level="INFO"):
        if self.debug_tab_manager:
            self.debug_tab_manager.update_transcription_log(message, level)

    def update_status_label(self, text: str, status_key: str = "info", duration: int = None):
        debug_log(f"UIManager.update_status_label (delegating to bar_manager): '{text}', key: '{status_key}', duration: {duration}", "UI")
        if self.bar_manager: 
            custom_text_payload = None
            if status_key == "custom_error_message" or status_key == "custom_success_message" or status_key == "transcription_result": # Added transcription_result
                custom_text_payload = text 
            self.bar_manager.update_status_on_canvas(text_key=status_key, custom_text=custom_text_payload)
        
        # If duration is specified, schedule a reset to initial prompt
        if duration and hasattr(self.app, 'root') and self.app.root.winfo_exists():
            self.app.root.after(duration, lambda: self.bar_manager.update_status_on_canvas(text_key="initial_prompt") if self.bar_manager else None)
        
        # Ensure logs also go to debug tab for important status updates
        # This part seems to be missing the actual logging call, let's ensure it logs.
        if "error" in status_key.lower():
             if self.debug_tab_manager: self.debug_tab_manager.update_transcription_log(f"Status Update (Error): {text}", "ERROR") # Changed to update_transcription_log
        elif "success" in status_key.lower() or status_key == "transcription_result": # Added transcription_result
             if self.debug_tab_manager: self.debug_tab_manager.update_transcription_log(f"Status Update (Success): {text}", "INFO") # Changed to update_transcription_log

    def on_window_resize(self, event):
        if event.widget == self.app.root:
            try:
                if hasattr(self, 'waveform_canvas') and self.waveform_canvas.winfo_exists():
                    if hasattr(self.app, 'recording_animation_active') and not self.app.recording_animation_active: 
                        self._draw_initial_prompt_text()
                
                # Debounced save is handled in app.py
                # if hasattr(self.app, 'ui_initialized') and self.app.ui_initialized:
                #     if event.width > 100 and event.height > 100: 
                #         self.app.save_window_size()
            except Exception: # Catch all for resize errors
                pass

    def _draw_initial_prompt_text(self):
        try:
            self.waveform_canvas.delete("all")
            w = self.waveform_canvas.winfo_width()
            h = self.waveform_canvas.winfo_height()
            if w > 1 and h > 1: # Ensure canvas has dimensions
                self.waveform_canvas.create_text(
                    w/2, h/2,
                    text="Speak after starting recording",
                    fill=self.colors["text_secondary"],
                    font=(self.MONO_FONT_FAMILY, 13),
                    tags="prompt_text"
                )
        except Exception as e:
            # print(f"Error drawing initial prompt: {e}") # Replaced debug_log
            pass

    def show_message(self, message, msg_type="info"):
        # print(f"UI: Showing message: {message} (type: {msg_type})") # Replaced debug_log
        
        if not hasattr(self.app, 'status_label') or not self.app.status_label:
            # print("UI: No status_label available") # Replaced debug_log
            return
            
        color_map = {
            "error": "#FF5555", "warning": "#FFAA00", "success": "#55FF7F", 
            "info": self.colors.get("text", "#FFFFFF"), "white": "#FFFFFF", 
            "red": "#FF5555", "green": "#55FF7F", "yellow": "#FFFF00", 
            "orange": "#FFAA00"
        }
        color = color_map.get(msg_type.lower(), self.colors.get("text", "#FFFFFF")) # Added .lower() and default
        
        self.app.status_label.configure(text=message, text_color=color)

    def toggle_log_section(self):
        """Toggle the dropdown panel (for F3 key binding)"""
        if self.dropdown_manager:
            self.dropdown_manager.toggle_dropdown_panel()

    def show_error(self, error_message):
        """Show error message to user"""
        self.update_main_status_message(f"Error: {error_message}", "ERROR")

    def refresh_theme(self):
        """Refresh all UI components with current theme"""
        if self.theme_manager:
            self.theme_manager.refresh_theme()
        if self.bar_manager:
            self.bar_manager.refresh_theme()
        if self.dropdown_manager:
            self.dropdown_manager.refresh_theme()
        if self.settings_tab_manager:
            self.settings_tab_manager.refresh_theme()
        if self.debug_tab_manager:
            self.debug_tab_manager.refresh_theme()
        if self.history_tab_manager:
            self.history_tab_manager.refresh_theme()
        debug_log("UIManager theme refresh complete.", "UI")

# Make sure to remove or comment out old UI elements from the original setup_ui 
# if they are no longer needed and were not explicitly removed in this diff.
# This diff focuses on adding the new structure and adapting existing methods. 