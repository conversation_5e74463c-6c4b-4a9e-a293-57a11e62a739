# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .... import core
from ....core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON><PERSON>per, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.request_pvc_manual_verification_response_model import RequestPvcManualVerificationResponseModel
from .captcha.client import AsyncCaptcha<PERSON><PERSON>, CaptchaClient
from .raw_client import AsyncRawVerificationClient, RawVerificationClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class VerificationClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawVerificationClient(client_wrapper=client_wrapper)
        self.captcha = CaptchaClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawVerificationClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawVerificationClient
        """
        return self._raw_client

    def request(
        self,
        voice_id: str,
        *,
        files: typing.List[core.File],
        extra_text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RequestPvcManualVerificationResponseModel:
        """
        Request manual verification for a PVC voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        files : typing.List[core.File]
            See core.File for more documentation

        extra_text : typing.Optional[str]
            Extra text to be used in the manual verification process.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RequestPvcManualVerificationResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.verification.request(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.request(
            voice_id, files=files, extra_text=extra_text, request_options=request_options
        )
        return _response.data


class AsyncVerificationClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawVerificationClient(client_wrapper=client_wrapper)
        self.captcha = AsyncCaptchaClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawVerificationClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawVerificationClient
        """
        return self._raw_client

    async def request(
        self,
        voice_id: str,
        *,
        files: typing.List[core.File],
        extra_text: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> RequestPvcManualVerificationResponseModel:
        """
        Request manual verification for a PVC voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        files : typing.List[core.File]
            See core.File for more documentation

        extra_text : typing.Optional[str]
            Extra text to be used in the manual verification process.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        RequestPvcManualVerificationResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.verification.request(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.request(
            voice_id, files=files, extra_text=extra_text, request_options=request_options
        )
        return _response.data
