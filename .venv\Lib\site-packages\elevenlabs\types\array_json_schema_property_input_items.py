# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

from .literal_json_schema_property import LiteralJsonSchemaProperty

if typing.TYPE_CHECKING:
    from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput
    from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput
ArrayJsonSchemaPropertyInputItems = typing.Union[
    LiteralJsonSchemaProperty, "ObjectJsonSchemaPropertyInput", "ArrayJsonSchemaPropertyInput"
]
