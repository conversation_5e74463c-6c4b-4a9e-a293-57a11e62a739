"""
Centralized Error Handling System for Whisp2

This module provides structured error handling with recovery strategies,
error boundaries, and graceful degradation for the voice transcription application.

Key Features:
- Centralized error logging and reporting
- Recovery strategies for different error types
- Error boundaries to prevent cascading failures
- User-friendly error messages
- Automatic retry mechanisms for transient errors
"""

import traceback
import threading
import time
from typing import Optional, Callable, Any, Dict, List
from enum import Enum
from dataclasses import dataclass
from src.core.logging_utils import debug_log, essential_log


class ErrorSeverity(Enum):
    """Error severity levels for categorizing errors."""
    LOW = "low"           # Minor issues that don't affect core functionality
    MEDIUM = "medium"     # Issues that affect some features but app remains usable
    HIGH = "high"         # Critical issues that affect core functionality
    CRITICAL = "critical" # Fatal errors that require immediate attention


class ErrorCategory(Enum):
    """Categories of errors for targeted recovery strategies."""
    AUDIO = "audio"                    # Audio recording/playback issues
    TRANSCRIPTION = "transcription"    # API transcription failures
    UI = "ui"                         # User interface errors
    NETWORK = "network"               # Network connectivity issues
    FILE_IO = "file_io"               # File system operations
    CONFIGURATION = "configuration"   # Settings and configuration errors
    SYSTEM = "system"                 # System-level errors
    UNKNOWN = "unknown"               # Unclassified errors


@dataclass
class ErrorContext:
    """Context information for error handling."""
    component: str
    operation: str
    user_message: str
    technical_details: str
    severity: ErrorSeverity
    category: ErrorCategory
    recoverable: bool = True
    retry_count: int = 0
    max_retries: int = 3


class ErrorHandler:
    """
    Centralized error handler with recovery strategies and error boundaries.
    
    This class provides a unified approach to error handling across the application,
    with automatic recovery strategies and graceful degradation.
    """
    
    def __init__(self, app_instance=None):
        """Initialize the error handler."""
        self.app = app_instance
        self.error_history: List[ErrorContext] = []
        self.recovery_strategies: Dict[ErrorCategory, Callable] = {}
        self.error_boundaries: Dict[str, bool] = {}
        self._setup_default_recovery_strategies()
    
    def _setup_default_recovery_strategies(self):
        """Set up default recovery strategies for different error categories."""
        self.recovery_strategies = {
            ErrorCategory.AUDIO: self._recover_audio_error,
            ErrorCategory.TRANSCRIPTION: self._recover_transcription_error,
            ErrorCategory.UI: self._recover_ui_error,
            ErrorCategory.NETWORK: self._recover_network_error,
            ErrorCategory.FILE_IO: self._recover_file_io_error,
            ErrorCategory.CONFIGURATION: self._recover_configuration_error,
            ErrorCategory.SYSTEM: self._recover_system_error,
            ErrorCategory.UNKNOWN: self._recover_unknown_error
        }
    
    def handle_error(self, 
                    error: Exception, 
                    context: ErrorContext,
                    callback: Optional[Callable] = None) -> bool:
        """
        Handle an error with appropriate recovery strategy.
        
        Args:
            error: The exception that occurred
            context: Error context information
            callback: Optional callback to execute after handling
            
        Returns:
            True if error was handled successfully, False otherwise
        """
        try:
            # Log the error
            self._log_error(error, context)
            
            # Add to error history
            self.error_history.append(context)
            
            # Check if we should attempt recovery
            if not context.recoverable or context.retry_count >= context.max_retries:
                self._handle_unrecoverable_error(error, context)
                return False
            
            # Attempt recovery
            recovery_success = self._attempt_recovery(error, context)
            
            # Execute callback if provided
            if callback:
                try:
                    callback(recovery_success, context)
                except Exception as callback_error:
                    debug_log(f"Error in error handler callback: {callback_error}", "ERROR")
            
            return recovery_success
            
        except Exception as handler_error:
            debug_log(f"Error in error handler itself: {handler_error}", "CRITICAL")
            return False
    
    def _log_error(self, error: Exception, context: ErrorContext):
        """Log error with appropriate severity level."""
        error_msg = f"[{context.component}] {context.operation}: {str(error)}"
        technical_details = f"Technical: {context.technical_details}\nTraceback: {traceback.format_exc()}"
        
        if context.severity == ErrorSeverity.CRITICAL:
            essential_log(f"🚨 CRITICAL ERROR: {error_msg}", "ERROR")
            debug_log(technical_details, "CRITICAL")
        elif context.severity == ErrorSeverity.HIGH:
            essential_log(f"❌ HIGH SEVERITY: {error_msg}", "ERROR")
            debug_log(technical_details, "ERROR")
        elif context.severity == ErrorSeverity.MEDIUM:
            essential_log(f"⚠️ MEDIUM SEVERITY: {error_msg}", "WARNING")
            debug_log(technical_details, "WARNING")
        else:
            debug_log(f"Low severity error: {error_msg}", "INFO")
            debug_log(technical_details, "DEBUG")
    
    def _attempt_recovery(self, error: Exception, context: ErrorContext) -> bool:
        """Attempt to recover from the error using appropriate strategy."""
        try:
            recovery_strategy = self.recovery_strategies.get(context.category)
            if recovery_strategy:
                debug_log(f"Attempting recovery for {context.category.value} error", "INFO")
                return recovery_strategy(error, context)
            else:
                debug_log(f"No recovery strategy for {context.category.value}", "WARNING")
                return False
        except Exception as recovery_error:
            debug_log(f"Recovery strategy failed: {recovery_error}", "ERROR")
            return False
    
    def _handle_unrecoverable_error(self, error: Exception, context: ErrorContext):
        """Handle errors that cannot be recovered from."""
        essential_log(f"💀 UNRECOVERABLE ERROR: {context.user_message}", "ERROR")
        
        # Update UI if possible
        if self.app and hasattr(self.app, 'ui_manager') and self.app.ui_manager:
            try:
                self.app.ui_manager.show_error(context.user_message)
            except:
                pass  # UI might be broken too
        
        # For critical errors, consider graceful shutdown
        if context.severity == ErrorSeverity.CRITICAL:
            debug_log("Critical error detected, considering graceful shutdown", "CRITICAL")
    
    # Recovery strategy implementations
    def _recover_audio_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from audio-related errors."""
        debug_log("Attempting audio error recovery", "INFO")
        
        try:
            if self.app and hasattr(self.app, 'audio_manager'):
                # Reset audio manager state
                self.app.audio_manager._stop_amplitude_worker()
                
                # Update UI to show audio issue
                if hasattr(self.app, 'ui_manager') and self.app.ui_manager:
                    self.app.ui_manager.update_main_status_message(
                        "Audio issue detected, please try again", "WARNING"
                    )
                
                return True
        except Exception as recovery_error:
            debug_log(f"Audio recovery failed: {recovery_error}", "ERROR")
        
        return False
    
    def _recover_transcription_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from transcription-related errors."""
        debug_log("Attempting transcription error recovery", "INFO")
        
        try:
            if self.app and hasattr(self.app, 'transcription_manager'):
                # Try to reload API client
                self.app.transcription_manager.reload_api_client()
                
                # Update UI
                if hasattr(self.app, 'ui_manager') and self.app.ui_manager:
                    self.app.ui_manager.update_main_status_message(
                        "Transcription service reconnected", "INFO"
                    )
                
                return True
        except Exception as recovery_error:
            debug_log(f"Transcription recovery failed: {recovery_error}", "ERROR")
        
        return False
    
    def _recover_ui_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from UI-related errors."""
        debug_log("Attempting UI error recovery", "INFO")
        
        try:
            # UI errors are often non-critical, just log and continue
            if self.app and hasattr(self.app, 'ui_manager'):
                # Try to refresh UI state
                self.app.ui_manager.update_main_status_message(
                    "Interface refreshed", "INFO"
                )
            
            return True
        except Exception as recovery_error:
            debug_log(f"UI recovery failed: {recovery_error}", "ERROR")
        
        return False
    
    def _recover_network_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from network-related errors."""
        debug_log("Attempting network error recovery", "INFO")
        
        # For network errors, we typically want to retry after a delay
        if context.retry_count < context.max_retries:
            delay = min(2 ** context.retry_count, 10)  # Exponential backoff, max 10s
            debug_log(f"Network error, retrying in {delay} seconds", "INFO")
            time.sleep(delay)
            return True
        
        return False
    
    def _recover_file_io_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from file I/O errors."""
        debug_log("Attempting file I/O error recovery", "INFO")
        
        # File I/O errors might be temporary, try once more
        if context.retry_count == 0:
            time.sleep(0.5)  # Brief delay
            return True
        
        return False
    
    def _recover_configuration_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from configuration errors."""
        debug_log("Attempting configuration error recovery", "INFO")
        
        try:
            if self.app and hasattr(self.app, 'settings_manager'):
                # Try to reload settings
                self.app.settings_manager.load_settings()
                return True
        except Exception as recovery_error:
            debug_log(f"Configuration recovery failed: {recovery_error}", "ERROR")
        
        return False
    
    def _recover_system_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from system-level errors."""
        debug_log("Attempting system error recovery", "INFO")
        
        # System errors are usually serious, limited recovery options
        return False
    
    def _recover_unknown_error(self, error: Exception, context: ErrorContext) -> bool:
        """Recover from unknown errors."""
        debug_log("Attempting unknown error recovery", "INFO")
        
        # For unknown errors, try a generic recovery approach
        try:
            if self.app:
                # Reset recording state if needed
                if hasattr(self.app, 'is_recording') and self.app.is_recording:
                    self.app.stop_recording_process()
                
                # Update UI
                if hasattr(self.app, 'ui_manager') and self.app.ui_manager:
                    self.app.ui_manager.update_main_status_message(
                        "System recovered from error", "INFO"
                    )
            
            return True
        except Exception as recovery_error:
            debug_log(f"Unknown error recovery failed: {recovery_error}", "ERROR")
        
        return False
    
    def create_error_boundary(self, boundary_name: str) -> bool:
        """Create an error boundary to prevent cascading failures."""
        if boundary_name in self.error_boundaries:
            return self.error_boundaries[boundary_name]
        
        self.error_boundaries[boundary_name] = True
        return True
    
    def is_error_boundary_active(self, boundary_name: str) -> bool:
        """Check if an error boundary is active."""
        return self.error_boundaries.get(boundary_name, True)
    
    def disable_error_boundary(self, boundary_name: str):
        """Disable an error boundary (typically after repeated failures)."""
        self.error_boundaries[boundary_name] = False
        debug_log(f"Error boundary '{boundary_name}' disabled due to repeated failures", "WARNING")


# Global error handler instance
_global_error_handler = None

def get_error_handler(app_instance=None) -> ErrorHandler:
    """Get the global error handler instance."""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler(app_instance)
    elif app_instance and not _global_error_handler.app:
        _global_error_handler.app = app_instance
    return _global_error_handler


def handle_error_with_context(component: str, 
                             operation: str,
                             error: Exception,
                             user_message: str,
                             severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                             category: ErrorCategory = ErrorCategory.UNKNOWN,
                             recoverable: bool = True) -> bool:
    """
    Convenience function to handle errors with context.
    
    Args:
        component: Name of the component where error occurred
        operation: Operation being performed when error occurred
        error: The exception that occurred
        user_message: User-friendly error message
        severity: Error severity level
        category: Error category for recovery strategy
        recoverable: Whether the error is recoverable
        
    Returns:
        True if error was handled successfully, False otherwise
    """
    context = ErrorContext(
        component=component,
        operation=operation,
        user_message=user_message,
        technical_details=str(error),
        severity=severity,
        category=category,
        recoverable=recoverable
    )
    
    error_handler = get_error_handler()
    return error_handler.handle_error(error, context)
