"""
Singleton API Client Manager for Whisp2 Voice Recording Application

This module provides a centralized, thread-safe singleton manager for all API clients
(Deepgram, OpenAI, Groq, ElevenLabs) to reduce memory usage and improve performance
by avoiding redundant client creation.

Key Features:
- Singleton pattern ensures only one instance per API type
- Thread-safe client creation and access
- Automatic client validation and recreation when needed
- Memory efficient client pooling
- Centralized API key management
"""

import threading
import json
import os
from typing import Dict, Optional, Any
from src.core.logging_utils import debug_log

# Import API clients with error handling
try:
    from deepgram import DeepgramClient
    DEEPGRAM_AVAILABLE = True
except ImportError:
    DeepgramClient = None
    DEEPGRAM_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OpenAI = None
    OPENAI_AVAILABLE = False

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    Groq = None
    GROQ_AVAILABLE = False

try:
    from elevenlabs.client import ElevenLabs
    ELEVENLABS_AVAILABLE = True
except ImportError:
    ElevenLabs = None
    ELEVENLABS_AVAILABLE = False


class APIClientManager:
    """
    Singleton manager for API clients with thread-safe access and automatic pooling.
    
    This class ensures that only one instance of each API client type exists,
    reducing memory usage and improving performance by avoiding redundant client creation.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Ensure singleton pattern with thread safety"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(APIClientManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the API client manager (only once due to singleton)"""
        if self._initialized:
            return
            
        self._initialized = True
        self._clients: Dict[str, Any] = {}
        self._api_keys: Dict[str, str] = {}
        self._client_locks: Dict[str, threading.Lock] = {
            'deepgram': threading.Lock(),
            'openai': threading.Lock(),
            'groq': threading.Lock(),
            'elevenlabs': threading.Lock()
        }
        
        # Client type mapping for validation
        self._client_types = {
            'deepgram': DeepgramClient if DEEPGRAM_AVAILABLE else None,
            'openai': OpenAI if OPENAI_AVAILABLE else None,
            'groq': Groq if GROQ_AVAILABLE else None,
            'elevenlabs': ElevenLabs if ELEVENLABS_AVAILABLE else None
        }
        
        debug_log("[API_CLIENT_MANAGER] Singleton instance initialized", "INFO")
    
    def load_settings(self, settings_file_path: str = "settings.json") -> None:
        """Load API keys from settings file"""
        try:
            if os.path.exists(settings_file_path):
                with open(settings_file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                apis = settings.get('apis', {})
                for api_name in ['deepgram', 'openai', 'groq', 'elevenlabs']:
                    api_key = apis.get(api_name, {}).get('api_key', '')
                    if api_key.strip():
                        self._api_keys[api_name] = api_key.strip()
                        debug_log(f"[API_CLIENT_MANAGER] Loaded API key for {api_name}", "INFO")
                    else:
                        debug_log(f"[API_CLIENT_MANAGER] No API key found for {api_name}", "WARN")
            else:
                debug_log(f"[API_CLIENT_MANAGER] Settings file not found: {settings_file_path}", "WARN")
                
        except Exception as e:
            debug_log(f"[API_CLIENT_MANAGER] Error loading settings: {e}", "ERROR")
    
    def get_client(self, api_name: str, force_recreate: bool = False) -> Optional[Any]:
        """
        Get or create an API client for the specified API.
        
        Args:
            api_name: Name of the API ('deepgram', 'openai', 'groq', 'elevenlabs')
            force_recreate: If True, recreate the client even if it exists
            
        Returns:
            API client instance or None if creation failed
        """
        if api_name not in self._client_locks:
            debug_log(f"[API_CLIENT_MANAGER] Unsupported API: {api_name}", "ERROR")
            return None
        
        with self._client_locks[api_name]:
            # Return existing client if available and not forcing recreation
            if not force_recreate and api_name in self._clients:
                client = self._clients[api_name]
                if self._validate_client(api_name, client):
                    debug_log(f"[API_CLIENT_MANAGER] Returning cached {api_name} client", "DEBUG")
                    return client
                else:
                    debug_log(f"[API_CLIENT_MANAGER] Cached {api_name} client invalid, recreating", "WARN")
            
            # Create new client
            return self._create_client(api_name)
    
    def _create_client(self, api_name: str) -> Optional[Any]:
        """Create a new API client for the specified API"""
        try:
            # Check if API is available
            client_type = self._client_types.get(api_name)
            if not client_type:
                debug_log(f"[API_CLIENT_MANAGER] {api_name} client not available (import failed)", "ERROR")
                return None
            
            # Get API key
            api_key = self._api_keys.get(api_name)
            if not api_key:
                debug_log(f"[API_CLIENT_MANAGER] No API key available for {api_name}", "ERROR")
                return None
            
            # Create client based on API type
            if api_name == 'deepgram':
                client = DeepgramClient(api_key)
            elif api_name == 'openai':
                client = OpenAI(api_key=api_key)
            elif api_name == 'groq':
                client = Groq(api_key=api_key)
            elif api_name == 'elevenlabs':
                client = ElevenLabs(api_key=api_key)
            else:
                debug_log(f"[API_CLIENT_MANAGER] Unknown API type: {api_name}", "ERROR")
                return None
            
            # Store and return client
            self._clients[api_name] = client
            debug_log(f"[API_CLIENT_MANAGER] Created new {api_name} client", "INFO")
            return client
            
        except Exception as e:
            debug_log(f"[API_CLIENT_MANAGER] Error creating {api_name} client: {e}", "ERROR")
            return None
    
    def _validate_client(self, api_name: str, client: Any) -> bool:
        """Validate that a client is of the correct type and functional"""
        try:
            expected_type = self._client_types.get(api_name)
            if not expected_type:
                return False
            
            return isinstance(client, expected_type)
            
        except Exception as e:
            debug_log(f"[API_CLIENT_MANAGER] Error validating {api_name} client: {e}", "ERROR")
            return False
    
    def update_api_key(self, api_name: str, api_key: str) -> bool:
        """
        Update API key for a specific API and recreate the client.
        
        Args:
            api_name: Name of the API
            api_key: New API key
            
        Returns:
            True if client was successfully recreated, False otherwise
        """
        if api_name not in self._client_locks:
            debug_log(f"[API_CLIENT_MANAGER] Unsupported API: {api_name}", "ERROR")
            return False
        
        with self._client_locks[api_name]:
            # Update API key
            self._api_keys[api_name] = api_key.strip()
            
            # Remove existing client to force recreation
            if api_name in self._clients:
                del self._clients[api_name]
            
            # Create new client with updated key
            client = self._create_client(api_name)
            success = client is not None
            
            debug_log(f"[API_CLIENT_MANAGER] Updated API key for {api_name}, client creation: {'success' if success else 'failed'}", "INFO")
            return success
    
    def clear_client(self, api_name: str) -> None:
        """Clear a specific client from the cache"""
        if api_name in self._client_locks:
            with self._client_locks[api_name]:
                if api_name in self._clients:
                    del self._clients[api_name]
                    debug_log(f"[API_CLIENT_MANAGER] Cleared {api_name} client from cache", "INFO")
    
    def clear_all_clients(self) -> None:
        """Clear all clients from the cache"""
        for api_name in self._client_locks.keys():
            self.clear_client(api_name)
        debug_log("[API_CLIENT_MANAGER] Cleared all clients from cache", "INFO")
    
    def get_client_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all cached clients"""
        info = {}
        for api_name in self._client_locks.keys():
            with self._client_locks[api_name]:
                has_client = api_name in self._clients
                has_key = api_name in self._api_keys
                client_type = type(self._clients[api_name]).__name__ if has_client else None
                
                info[api_name] = {
                    'has_client': has_client,
                    'has_api_key': has_key,
                    'client_type': client_type,
                    'available': self._client_types[api_name] is not None
                }
        
        return info


# Global singleton instance
_api_client_manager = None

def get_api_client_manager() -> APIClientManager:
    """Get the global singleton API client manager instance"""
    global _api_client_manager
    if _api_client_manager is None:
        _api_client_manager = APIClientManager()
    return _api_client_manager
