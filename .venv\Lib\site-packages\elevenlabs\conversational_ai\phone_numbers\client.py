# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rap<PERSON>, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.create_phone_number_response_model import CreatePhoneNumberResponseModel
from .raw_client import AsyncRawPhoneNumbersClient, RawPhoneNumbersClient
from .types.phone_numbers_create_request_body import PhoneNumbersCreateRequestBody
from .types.phone_numbers_get_response import PhoneNumbersGetResponse
from .types.phone_numbers_list_response_item import PhoneNumbersListResponseItem
from .types.phone_numbers_update_response import PhoneNumbersUpdateResponse

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PhoneNumbersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPhoneNumbersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPhoneNumbersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPhoneNumbersClient
        """
        return self._raw_client

    def create(
        self, *, request: PhoneNumbersCreateRequestBody, request_options: typing.Optional[RequestOptions] = None
    ) -> CreatePhoneNumberResponseModel:
        """
        Import Phone Number from provider configuration (Twilio or SIP trunk)

        Parameters
        ----------
        request : PhoneNumbersCreateRequestBody

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreatePhoneNumberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs
        from elevenlabs.conversational_ai.phone_numbers import (
            PhoneNumbersCreateRequestBody_Twilio,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.phone_numbers.create(
            request=PhoneNumbersCreateRequestBody_Twilio(
                phone_number="phone_number",
                label="label",
                sid="sid",
                token="token",
            ),
        )
        """
        _response = self._raw_client.create(request=request, request_options=request_options)
        return _response.data

    def get(
        self, phone_number_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> PhoneNumbersGetResponse:
        """
        Retrieve Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PhoneNumbersGetResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.phone_numbers.get(
            phone_number_id="TeaqRRdTcIfIu2i7BYfT",
        )
        """
        _response = self._raw_client.get(phone_number_id, request_options=request_options)
        return _response.data

    def delete(
        self, phone_number_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete Phone Number by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.phone_numbers.delete(
            phone_number_id="TeaqRRdTcIfIu2i7BYfT",
        )
        """
        _response = self._raw_client.delete(phone_number_id, request_options=request_options)
        return _response.data

    def update(
        self,
        phone_number_id: str,
        *,
        agent_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PhoneNumbersUpdateResponse:
        """
        Update Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        agent_id : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PhoneNumbersUpdateResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.phone_numbers.update(
            phone_number_id="TeaqRRdTcIfIu2i7BYfT",
        )
        """
        _response = self._raw_client.update(phone_number_id, agent_id=agent_id, request_options=request_options)
        return _response.data

    def list(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[PhoneNumbersListResponseItem]:
        """
        Retrieve all Phone Numbers

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[PhoneNumbersListResponseItem]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.phone_numbers.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data


class AsyncPhoneNumbersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPhoneNumbersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPhoneNumbersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPhoneNumbersClient
        """
        return self._raw_client

    async def create(
        self, *, request: PhoneNumbersCreateRequestBody, request_options: typing.Optional[RequestOptions] = None
    ) -> CreatePhoneNumberResponseModel:
        """
        Import Phone Number from provider configuration (Twilio or SIP trunk)

        Parameters
        ----------
        request : PhoneNumbersCreateRequestBody

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreatePhoneNumberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs
        from elevenlabs.conversational_ai.phone_numbers import (
            PhoneNumbersCreateRequestBody_Twilio,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.phone_numbers.create(
                request=PhoneNumbersCreateRequestBody_Twilio(
                    phone_number="phone_number",
                    label="label",
                    sid="sid",
                    token="token",
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(request=request, request_options=request_options)
        return _response.data

    async def get(
        self, phone_number_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> PhoneNumbersGetResponse:
        """
        Retrieve Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PhoneNumbersGetResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.phone_numbers.get(
                phone_number_id="TeaqRRdTcIfIu2i7BYfT",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(phone_number_id, request_options=request_options)
        return _response.data

    async def delete(
        self, phone_number_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete Phone Number by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.phone_numbers.delete(
                phone_number_id="TeaqRRdTcIfIu2i7BYfT",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(phone_number_id, request_options=request_options)
        return _response.data

    async def update(
        self,
        phone_number_id: str,
        *,
        agent_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PhoneNumbersUpdateResponse:
        """
        Update Phone Number details by ID

        Parameters
        ----------
        phone_number_id : str
            The id of an agent. This is returned on agent creation.

        agent_id : typing.Optional[str]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PhoneNumbersUpdateResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.phone_numbers.update(
                phone_number_id="TeaqRRdTcIfIu2i7BYfT",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(phone_number_id, agent_id=agent_id, request_options=request_options)
        return _response.data

    async def list(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[PhoneNumbersListResponseItem]:
        """
        Retrieve all Phone Numbers

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[PhoneNumbersListResponseItem]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.phone_numbers.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data
