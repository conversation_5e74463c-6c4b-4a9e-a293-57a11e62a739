import json
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import os
import sys

# Import debug_log from new location
from src.core.logging_utils import debug_log

class SettingsDialog:
    def __init__(self, parent, on_settings_saved=None):
        debug_log("Opening settings dialog")
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("Settings")
        self.dialog.geometry("400x250")
        self.dialog.transient(parent)
        self.dialog.resizable(False, False)
        self.on_settings_saved = on_settings_saved
        self.settings_path = self.find_settings_file()
        self.MONO_FONT_FAMILY = "Consolas"

        # Load current settings
        try:
            with open(self.settings_path, 'r') as f:
                self.settings = json.load(f)
                debug_log(f"Loaded settings from {self.settings_path}")
        except Exception as e:
            debug_log(f"Error loading settings: {e}")
            self.settings = {
                'active_api': 'deepgram',
                'apis': {
                    'deepgram': {'api_key': ''},
                    'openai': {'api_key': ''},
                    'groq': {'api_key': ''},
                    'elevenlabs': {'api_key': ''}
                }
            }

        self.api_frame = ctk.CTkFrame(self.dialog)
        self.api_frame.pack(fill="x", padx=20, pady=20)

        self.api_label = ctk.CTkLabel(
            self.api_frame,
            text="Transcription API:",
            font=ctk.CTkFont(family=self.MONO_FONT_FAMILY, size=14)
        )
        self.api_label.pack(anchor="w", pady=5)

        self.api_select_var = tk.StringVar()
        self.api_select_dropdown = ctk.CTkOptionMenu(
            self.api_frame,
            variable=self.api_select_var,
            values=["deepgram", "openai", "groq", "elevenlabs"],
            width=200,
            font=(self.MONO_FONT_FAMILY, 12),
            dropdown_font=(self.MONO_FONT_FAMILY, 12),
            command=self.on_api_select
        )
        self.api_select_dropdown.pack(pady=5)

        self.api_key_label = ctk.CTkLabel(
            self.api_frame,
            text="API Key:",
            font=ctk.CTkFont(family=self.MONO_FONT_FAMILY, size=14)
        )
        self.api_key_label.pack(anchor="w", pady=5)

        self.api_key_var = tk.StringVar()
        self.api_entry = ctk.CTkEntry(
            self.api_frame,
            width=300,
            font=ctk.CTkFont(family=self.MONO_FONT_FAMILY, size=14),
            textvariable=self.api_key_var
        )
        self.api_entry.pack(pady=5)

        self.save_btn = ctk.CTkButton(
            self.dialog,
            text="Save",
            command=self.save_settings,
            font=(self.MONO_FONT_FAMILY, 12),
            width=100
        )
        self.save_btn.pack(pady=20)

        # Initialize dropdown and key
        active_api = self.settings.get('active_api', 'deepgram')
        self.api_select_var.set(active_api)
        self.api_select_dropdown.set(active_api)
        self.api_key_var.set(self.settings.get('apis', {}).get(active_api, {}).get('api_key', ''))

    def find_settings_file(self):
        """Find the settings file in multiple possible locations"""
        possible_paths = [
            'settings.json',
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'settings.json')
        ]
        
        # Try to find an existing settings file
        for path in possible_paths:
            if os.path.exists(path):
                debug_log(f"Found settings file at: {path}")
                return path
                
        # Default to the local path if not found
        debug_log("Settings file not found, defaulting to local path")
        return 'settings.json'

    def on_api_select(self, selected_api):
        debug_log(f"API selection changed to: {selected_api}")
        self.api_key_var.set(self.settings.get('apis', {}).get(selected_api, {}).get('api_key', ''))

    def save_settings(self):
        selected_api = self.api_select_var.get()
        api_key = self.api_key_var.get()
        
        debug_log(f"Saving settings with API: {selected_api}")
        
        # Validate API key
        if not api_key.strip():
            messagebox.showerror("Error", "API key cannot be empty")
            return
            
        if 'apis' not in self.settings:
            self.settings['apis'] = {}
        if selected_api not in self.settings['apis']:
            self.settings['apis'][selected_api] = {}
        self.settings['apis'][selected_api]['api_key'] = api_key
        
        # Only set the active API after validating the key
        old_api = self.settings.get('active_api')
        self.settings['active_api'] = selected_api
        
        try:
            debug_log(f"Writing settings to {self.settings_path}")
            with open(self.settings_path, 'w') as f:
                json.dump(self.settings, f, indent=2)
            
            debug_log(f"Settings saved successfully. API changed from {old_api} to {selected_api}")
            
            # Find the main app instance
            parent = self.dialog.master
            if hasattr(parent, 'reload_api_client'):
                debug_log("Directly calling parent.reload_api_client()")
                parent.reload_api_client()
            elif hasattr(parent, 'app') and hasattr(parent.app, 'reload_api_client'):
                debug_log("Calling parent.app.reload_api_client()")
                parent.app.reload_api_client()
            
            # Notify callback if provided
            if self.on_settings_saved:
                debug_log("Calling on_settings_saved callback")
                self.on_settings_saved()
                
            messagebox.showinfo("Success", "Settings saved successfully")
            self.dialog.destroy()
        except Exception as e:
            debug_log(f"Failed to save settings: {e}")
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}") 