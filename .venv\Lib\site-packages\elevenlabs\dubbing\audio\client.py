# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, Sync<PERSON><PERSON><PERSON>rapper
from ...core.request_options import RequestOptions
from .raw_client import AsyncRawAudioClient, RawAudioClient


class AudioClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawAudioClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawAudioClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawAudioClient
        """
        return self._raw_client

    def get(
        self, dubbing_id: str, language_code: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Iterator[bytes]:
        """
        Returns dub as a streamed MP3 or MP4 file. If this dub has been edited using Dubbing Studio you need to use the resource render endpoint as this endpoint only returns the original automatic dub result.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            The dubbed audio or video file
        """
        with self._raw_client.get(dubbing_id, language_code, request_options=request_options) as r:
            yield from r.data


class AsyncAudioClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawAudioClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawAudioClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawAudioClient
        """
        return self._raw_client

    async def get(
        self, dubbing_id: str, language_code: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.AsyncIterator[bytes]:
        """
        Returns dub as a streamed MP3 or MP4 file. If this dub has been edited using Dubbing Studio you need to use the resource render endpoint as this endpoint only returns the original automatic dub result.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language_code : str
            ID of the language.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            The dubbed audio or video file
        """
        async with self._raw_client.get(dubbing_id, language_code, request_options=request_options) as r:
            async for _chunk in r.data:
                yield _chunk
